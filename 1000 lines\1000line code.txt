'use client'

import React, { useState, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import svgPaths from "../imports/svg-ef293kw09m";
import imgRoundCubeBlackMatte from "figma:asset/212e624bfa920a2f5c150ed2c48f40748de5ec5f.png";
import imgSphereIridescent from "figma:asset/71f8940d5885ea4b99defeb5890ef3cd386126b3.png";
import img from "figma:asset/6d7dce2f8945a9d3d48432859747dd0846373443.png";
import img1 from "figma:asset/b5e59d7a2371454573c4fb23ccaa2c5bd9eb7297.png";
import imgLilcodermanPinkChairSittingOnTableInARoomInTheStyle8F5E5Aa938F84Af089F38572B0Ae93621 from "figma:asset/bdf05f127e983c7a87434379e33db88c745be221.png";
import imgImage from "figma:asset/ab325d5d46b804b1ce04547ea0b4bd6703d39370.png";
import imgRectangle31 from "figma:asset/d18f3ca1c09872c27e4fb88ba1980b61a0d8dbd5.png";
import imgRectangle28 from "figma:asset/b4af99a3c7f40f4c045a5748c8d707912e22f2c1.png";
import imgRectangle26 from "figma:asset/a9014fc1fa453c03c2c52f91fff1f2754e20ad58.png";

function AnimatedCube({ children, delay = 0, ...props }) {
  return (
    <motion.div
      {...props}
      initial={{ rotateX: 0, rotateY: 0 }}
      animate={{ 
        rotateX: [0, 10, -10, 0],
        rotateY: [0, 15, -15, 0] 
      }}
      transition={{
        duration: 6,
        repeat: Infinity,
        delay,
        ease: "easeInOut"
      }}
    >
      {children}
    </motion.div>
  );
}

function AnimatedSphere({ children, delay = 0, ...props }) {
  return (
    <motion.div
      {...props}
      initial={{ scale: 1 }}
      animate={{ scale: [1, 1.1, 0.9, 1] }}
      transition={{
        duration: 4,
        repeat: Infinity,
        delay,
        ease: "easeInOut"
      }}
    >
      {children}
    </motion.div>
  );
}

function Cube2() {
  return (
    <div className="relative size-full" data-name="Cube-2">
      <div
        className="absolute bg-center bg-cover bg-no-repeat blur-[2px] filter inset-0"
        data-name="RoundCube-Black-Matte"
        style={{ backgroundImage: `url('${imgRoundCubeBlackMatte}')` }}
      />
    </div>
  );
}

function Cube3() {
  return (
    <div className="relative size-full" data-name="Cube-2">
      <div
        className="absolute bg-center bg-cover bg-no-repeat inset-0"
        data-name="RoundCube-Black-Matte"
        style={{ backgroundImage: `url('${imgRoundCubeBlackMatte}')` }}
      />
    </div>
  );
}

function Shapes() {
  const { scrollYProgress } = useScroll();
  const rotateY = useTransform(scrollYProgress, [0, 1], [0, 360]);
  
  return (
    <div
      className="absolute h-[719px] top-6 w-[673px]"
      data-name="Shapes"
      style={{ left: "calc(25% + 45px)" }}
    >
      <motion.div 
        className="absolute bottom-[1.91%] flex items-center justify-center left-[90.359%] right-[-7.412%] top-[82.56%]"
        style={{ rotateY }}
      >
        <AnimatedSphere delay={0} className="flex-none h-[83.202px] rotate-[332.946deg] skew-x-[1.293deg] w-[84.508px]">
          <div
            className="bg-center bg-cover bg-no-repeat size-full"
            data-name="Sphere-Iridescent"
            style={{ backgroundImage: `url('${imgSphereIridescent}')` }}
          />
        </AnimatedSphere>
      </motion.div>
      
      <motion.div 
        className="absolute bottom-[81.209%] flex items-center justify-center left-[-74.889%] right-[124.732%] top-[-26.886%]"
        initial={{ opacity: 0.7 }}
        animate={{ opacity: [0.7, 1, 0.7] }}
        transition={{ duration: 3, repeat: Infinity }}
      >
        <AnimatedSphere delay={1} className="flex-none h-[244.711px] rotate-[332.946deg] skew-x-[1.293deg] w-[248.554px]">
          <div
            className="bg-center bg-cover bg-no-repeat blur-[2.4px] filter size-full"
            data-name="Sphere-Iridescent"
            style={{ backgroundImage: `url('${imgSphereIridescent}')` }}
          />
        </AnimatedSphere>
      </motion.div>
      
      <div className="absolute flex h-[389.957px] items-center justify-center left-[-74.889%] right-[118.057%] top-[482px]">
        <AnimatedCube delay={0.5} className="flex-none h-[313.432px] rotate-[341.68deg] skew-x-[0.465deg] w-[296.572px]">
          <Cube2 />
        </AnimatedCube>
      </div>
      
      <div className="absolute flex h-[63.31px] items-center justify-center left-[13.133%] right-[77.332%] top-[261.527px]">
        <AnimatedCube delay={1} className="flex-none h-[50.069px] rotate-[341.68deg] skew-x-[0.465deg] w-[50.609px]">
          <Cube3 />
        </AnimatedCube>
      </div>
      
      <div className="absolute flex h-[70.701px] items-center justify-center left-[96.021%] right-[-6.669%] top-[411.527px]">
        <AnimatedCube delay={1.5} className="flex-none h-[50.351px] rotate-[45.541deg] skew-x-[359.229deg] w-[50.329px]">
          <Cube3 />
        </AnimatedCube>
      </div>
      
      <div className="absolute flex h-[171.353px] items-center justify-center left-[133.923%] right-[-61.746%] top-[-23.409px]">
        <AnimatedCube delay={2} className="flex-none h-[166.742px] rotate-[261.528deg] skew-x-[359.453deg] w-[150px]">
          <Cube2 />
        </AnimatedCube>
      </div>
      
      <div className="absolute flex h-[105.821px] items-center justify-center left-[-15.453%] right-[99.942%] top-[482px]">
        <AnimatedCube delay={2.5} className="flex-none h-[77.798px] rotate-[151.763deg] skew-x-[359.229deg] w-[77.764px]">
          <Cube3 />
        </AnimatedCube>
      </div>
    </div>
  );
}

function NavigationLinks() {
  const [activeLink, setActiveLink] = useState('');
  
  const scrollToSection = (sectionId: string) => {
    setActiveLink(sectionId);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div
      className="box-border content-stretch flex flex-row font-['Montserrat:Bold',_sans-serif] font-bold gap-8 h-[50px] items-center justify-start p-0 relative shrink-0 text-[#ffffff] text-[18px] text-left text-nowrap w-auto"
      data-name="Links"
    >
      {['About', 'Services', 'Our Work', 'Blog'].map((link, index) => {
        const sectionId = link.toLowerCase().replace(' ', '-');
        return (
          <motion.div 
            key={link}
            className="relative shrink-0 cursor-pointer"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => scrollToSection(sectionId)}
          >
            <p className={`block leading-[1.3] text-nowrap whitespace-pre transition-colors duration-300 ${
              activeLink === sectionId ? 'text-[#feb273]' : 'hover:text-[#feb273]'
            }`}>
              {link}
            </p>
          </motion.div>
        );
      })}
    </div>
  );
}

function LetsTalk() {
  return (
    <motion.div
      className="grid-cols-[max-content] grid-rows-[max-content] inline-grid place-items-start relative shrink-0 cursor-pointer z-10"
      data-name="Lets Talk"
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={() => {
        const contactSection = document.getElementById('contact');
        if (contactSection) {
          contactSection.scrollIntoView({ behavior: 'smooth' });
        }
      }}
    >
      <div
        className="[background-size:126.44%_111.86%] [grid-area:1_/_1] bg-[50.72%_100%] bg-no-repeat h-[24px] mask-alpha mask-intersect mask-no-clip mask-no-repeat mask-position-[0px_0px] mask-size-[128px_24px] w-[128px]"
        data-name="画形态"
        style={{
          backgroundImage: `url('${img}')`,
          maskImage: `url('${img1}')`,
        }}
      />
      <div
        className="[grid-area:1_/_1] font-['Montserrat:Bold',_sans-serif] font-bold leading-[0] mask-alpha mask-intersect mask-no-clip mask-no-repeat mask-position-[0px_0px] mask-size-[128px_24px] relative text-[#ffffff] text-[16px] text-left text-nowrap"
        style={{ maskImage: `url('${img1}')` }}
      >
        <p className="block leading-[1.5] whitespace-pre text-center pt-1" dir="auto">
          Let's Talk
        </p>
      </div>
    </motion.div>
  );
}

function Navbar() {
  return (
    <div
      className="absolute box-border content-stretch flex flex-row h-[51.123px] items-center justify-between leading-[0] left-0 px-[124px] py-0 top-[31.035px] w-[1437px]"
      data-name="Navbar"
    >
      <NavigationLinks />
      <LetsTalk />
    </div>
  );
}

function Navbar1() {
  return (
    <div className="absolute contents left-0 top-[31.035px]" data-name="Navbar">
      <motion.div
        className="absolute bg-[#131d26] h-[44.988px] rounded-[25px] top-[31.104px] w-[836px]"
        style={{ left: "calc(25% - 21px)" }}
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      />
      <Navbar />
    </div>
  );
}

function Middle() {
  return (
    <div
      className="absolute contents top-[31px]"
      data-name="Middle"
      style={{ left: "calc(8.333% + 28px)" }}
    >
      <motion.div
        className="absolute h-[405.153px] top-[337.282px] w-[811.779px]"
        style={{ left: "calc(25% - 23px)" }}
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.4 }}
      >
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 812 406"
        >
          <path
            d={svgPaths.p3a293080}
            fill="var(--fill-0, #FEB273)"
            id="Ellipse 2"
          />
        </svg>
      </motion.div>
      
      <motion.div
        className="absolute flex h-[405.153px] items-center justify-center top-[737.282px] w-[811.779px]"
        style={{ left: "calc(25% - 23px)" }}
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.6 }}
      >
        <div className="flex-none rotate-[180deg]">
          <div className="h-[405.153px] relative w-[811.779px]">
            <svg
              className="block size-full"
              fill="none"
              preserveAspectRatio="none"
              viewBox="0 0 812 406"
            >
              <path
                d={svgPaths.p3a293080}
                fill="var(--fill-0, #FEB273)"
                id="Ellipse 3"
              />
            </svg>
          </div>
        </div>
      </motion.div>
      
      <motion.div
        className="absolute font-['Playfair_Display:Bold',_sans-serif] font-bold leading-[0] text-[#ffffff] text-[250px] text-left text-nowrap top-[337px]"
        style={{ left: "calc(16.667% + 68px)" }}
        initial={{ x: -100, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 1, delay: 0.8 }}
      >
        <p className="block leading-[normal] whitespace-pre">Design</p>
      </motion.div>
      
      <motion.div
        className="absolute font-['Playfair_Display:Bold',_sans-serif] font-bold leading-[0] text-[#131d26] text-[309.341px] text-left text-nowrap top-[31px]"
        style={{ left: "calc(8.333% + 28px)" }}
        initial={{ x: 100, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 1, delay: 1 }}
      >
        <p className="block leading-[normal] whitespace-pre">Creative</p>
      </motion.div>
    </div>
  );
}

function IconOutlinedDirectionsStraightArrowsUpRight() {
  return (
    <div
      className="relative shrink-0 size-[42px]"
      data-name="icon / outlined / directions / straight arrows / up right"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 42 42"
      >
        <g id="icon / outlined / directions / straight arrows / up right">
          <path
            d="M12.25 29.75L29.75 12.25"
            id="Vector"
            stroke="var(--stroke-0, white)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          />
          <path
            d="M12.25 12.25H29.75V29.75"
            id="Vector_2"
            stroke="var(--stroke-0, white)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          />
        </g>
      </svg>
    </div>
  );
}

function Portfolio() {
  return (
    <motion.div
      className="bg-[#131d26] relative rounded-[60px] shrink-0 w-52 cursor-pointer"
      data-name="Portfolio"
      whileHover={{ scale: 1.05, backgroundColor: "#1a2530" }}
      whileTap={{ scale: 0.95 }}
      onClick={() => {
        const portfolioSection = document.getElementById('our-work');
        if (portfolioSection) {
          portfolioSection.scrollIntoView({ behavior: 'smooth' });
        }
      }}
    >
      <div className="box-border content-stretch flex flex-row items-center justify-center overflow-clip px-5 py-2.5 relative w-52">
        <div className="font-['Lufga:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#ffffff] text-[25.692px] text-left text-nowrap tracking-[-0.3854px]">
          <p className="adjustLetterSpacing block leading-[normal] whitespace-pre">
            Portfolio
          </p>
        </div>
        <motion.div
          animate={{ x: [0, 3, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <IconOutlinedDirectionsStraightArrowsUpRight />
        </motion.div>
      </div>
      <div className="absolute border-[#d0d5dd] border-[0.5px] border-solid inset-0 pointer-events-none rounded-[60px]" />
    </motion.div>
  );
}

function HireMe() {
  return (
    <motion.div
      className="basis-0 grow min-h-px min-w-px relative rounded-[60px] shrink-0 cursor-pointer"
      data-name="Hire me"
      whileHover={{ backgroundColor: "#feb273" }}
      whileTap={{ scale: 0.95 }}
      onClick={() => {
        const contactSection = document.getElementById('contact');
        if (contactSection) {
          contactSection.scrollIntoView({ behavior: 'smooth' });
        }
      }}
    >
      <div className="flex flex-row items-center justify-center overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-center px-5 py-2.5 relative w-full">
          <div className="font-['Lufga:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#131d26] text-[25.692px] text-left text-nowrap tracking-[-0.3854px]">
            <p className="adjustLetterSpacing block leading-[normal] whitespace-pre">
              Hire me
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

function Button5() {
  return (
    <motion.div
      className="absolute backdrop-blur-[7.5px] backdrop-filter bg-[rgba(255,255,255,0.1)] h-[83.841px] rounded-[50px] top-[617.43px] translate-x-[-50%] w-[348px]"
      data-name="Button"
      style={{ left: "calc(54.167% - 44px)" }}
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, delay: 1.2 }}
      whileHover={{ y: -5 }}
    >
      <div className="box-border content-stretch flex flex-row gap-2.5 h-[83.841px] items-center justify-center overflow-clip p-[10px] relative w-[348px]">
        <Portfolio />
        <HireMe />
      </div>
      <div className="absolute border-2 border-[#ffffff] border-solid inset-0 pointer-events-none rounded-[50px]" />
    </motion.div>
  );
}

function VuesaxBoldQuoteUp() {
  return (
    <div className="absolute contents inset-0" data-name="vuesax/bold/quote-up">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 36 36"
      >
        <g id="quote-up">
          <g id="Vector" opacity="0"></g>
          <path
            d={svgPaths.p35158000}
            fill="var(--fill-0, #344054)"
            id="Vector_2"
          />
          <path
            d={svgPaths.p2b170700}
            fill="var(--fill-0, #344054)"
            id="Vector_3"
          />
        </g>
      </svg>
    </div>
  );
}

function VuesaxBoldQuoteUp1() {
  return (
    <div className="relative shrink-0 size-9" data-name="vuesax/bold/quote-up">
      <VuesaxBoldQuoteUp />
    </div>
  );
}

function Frame4() {
  return (
    <motion.div 
      className="absolute box-border content-stretch flex flex-col gap-6 h-[138.734px] items-start justify-start left-[49px] p-0 top-[371px]"
      initial={{ x: -50, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: 0.8, delay: 1.4 }}
    >
      <VuesaxBoldQuoteUp1 />
      <div className="font-['Lufga:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#344054] text-[20px] text-left text-nowrap tracking-[-0.3px]">
        <p className="adjustLetterSpacing block leading-[normal] whitespace-pre">
          Jenny's Exceptional product design
          <br />
          ensure our website's success.
          <br />
          Highly Recommended
        </p>
      </div>
    </motion.div>
  );
}

function Star() {
  return (
    <motion.div
      className="absolute bottom-[10.411%] left-[8.331%] right-[8.324%] top-[10.417%]"
      data-name="Star"
      animate={{ rotate: [0, 360] }}
      transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 27 26"
      >
        <g id="Star">
          <path
            d={svgPaths.p6049d00}
            fill="var(--fill-0, #FD853A)"
            id="Star_2"
          />
        </g>
      </svg>
    </motion.div>
  );
}

function IconStar() {
  return (
    <div className="relative shrink-0 size-8" data-name="icon/Star">
      <Star />
    </div>
  );
}

function Frame5() {
  return (
    <div className="box-border content-stretch flex flex-row items-start justify-center p-0 relative shrink-0">
      {[...Array(5).keys()].map((_, i) => (
        <motion.div
          key={i}
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ 
            duration: 0.5, 
            delay: 1.6 + (i * 0.1),
            type: "spring",
            stiffness: 260,
            damping: 20
          }}
        >
          <IconStar />
        </motion.div>
      ))}
    </div>
  );
}

function Frame3() {
  return (
    <motion.div 
      className="box-border content-stretch flex flex-col gap-[5px] items-end justify-start leading-[0] p-0 relative shrink-0 text-center text-neutral-900 text-nowrap"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.6, delay: 2.1, type: "spring" }}
    >
      <div className="font-['Urbanist:Bold',_sans-serif] font-bold relative shrink-0 text-[47px] tracking-[-0.705px]">
        <motion.p 
          className="adjustLetterSpacing block leading-none text-nowrap whitespace-pre"
          animate={{ color: ["#000000", "#feb273", "#000000"] }}
          transition={{ duration: 2, repeat: Infinity, delay: 3 }}
        >
          10 Years
        </motion.p>
      </div>
      <div className="font-['Lufga:Regular',_sans-serif] not-italic relative shrink-0 text-[20px] tracking-[-0.3px]">
        <p className="adjustLetterSpacing block leading-none text-nowrap whitespace-pre">
          Experience
        </p>
      </div>
    </motion.div>
  );
}

function Frame6() {
  return (
    <div
      className="absolute box-border content-stretch flex flex-col gap-[21px] h-[125.665px] items-end justify-start p-0 top-[605px]"
      style={{ left: "calc(83.333% + 51px)" }}
    >
      <Frame5 />
      <Frame3 />
    </div>
  );
}

function HeroSection() {
  return (
    <section id="hero" className="absolute contents left-0 top-6" data-name="Hero Section">
      <Shapes />
      <Navbar1 />
      <Middle />
      <Button5 />
      <Frame4 />
      <Frame6 />
    </section>
  );
}

// Social Media Icons Components
function InstagramIcon() {
  return (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
    </svg>
  );
}

function LinkedInIcon() {
  return (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
    </svg>
  );
}

function TwitterIcon() {
  return (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
    </svg>
  );
}

function SocialMediaButton({ icon, platform, delay = 0 }) {
  const handleClick = () => {
    // Handle social media navigation
    const urls = {
      instagram: 'https://instagram.com',
      linkedin: 'https://linkedin.com',
      twitter: 'https://twitter.com'
    };
    window.open(urls[platform], '_blank');
  };

  return (
    <motion.button
      className="bg-[rgba(255,255,255,0.2)] backdrop-blur-sm rounded-full p-3 hover:bg-[rgba(255,255,255,0.3)] transition-all duration-300 cursor-pointer group"
      initial={{ scale: 0, opacity: 0 }}
      whileInView={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.5, delay, type: "spring", stiffness: 200 }}
      whileHover={{ scale: 1.1, y: -2 }}
      whileTap={{ scale: 0.95 }}
      onClick={handleClick}
      viewport={{ once: true }}
    >
      <div className="text-[#000000] group-hover:text-[#feb273] transition-colors duration-300">
        {icon}
      </div>
    </motion.button>
  );
}

export default function InteractiveDigitalAgency() {
  const [currentSection, setCurrentSection] = useState('hero');

  useEffect(() => {
    const handleScroll = () => {
      const sections = ['hero', 'about', 'our-work', 'contact', 'blog'];
      const scrollPosition = window.scrollY + window.innerHeight / 2;

      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setCurrentSection(section);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div
      className="bg-[#ffffff] relative size-full"
      data-name="Digital Agency Website"
    >
      <HeroSection />
      
      {/* About Section */}
      <section id="about" className="absolute contents left-[22px] top-[760px]" data-name="Section 2">
        {/* Slogan/Intro */}
        <motion.div
          className="absolute bg-[#fadcd9] h-[479px] left-[22px] overflow-hidden rounded-[20px] top-[760px] w-[565px]"
          data-name="SLOGAN/INTRO"
          initial={{ x: -50, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="absolute font-['Gilroy:Bold',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[56px] text-left w-[475px]"
            style={{ top: "calc(50% - 50px)" }}
            whileHover={{ scale: 1.02 }}
          >
            <p className="leading-[60px]">
              <span>Artist Redefining </span>
              <motion.span 
                className="font-['Gilroy:Light_Italic',_sans-serif] italic"
                animate={{ color: ["#000000", "#feb273", "#000000"] }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                Architecture
              </motion.span>
              <span> with AI-Driven Design</span>
            </p>
          </motion.div>
          
          <motion.div
            className="absolute right-6 size-[119px] top-[34px]"
            data-name="FLOWER ICON"
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
          >
            <svg
              className="block size-full"
              fill="none"
              preserveAspectRatio="none"
              viewBox="0 0 119 119"
            >
              <g clipPath="url(#clip0_1_366)" id="FLOWER ICON">
                <path
                  d={svgPaths.p12a08980}
                  fill="var(--fill-0, #F8AFA6)"
                  id="Vector"
                />
              </g>
              <defs>
                <clipPath id="clip0_1_366">
                  <rect fill="white" height="119" width="119" />
                </clipPath>
              </defs>
            </svg>
          </motion.div>
        </motion.div>

        {/* Portrait */}
        <motion.div
          className="absolute h-[476px] overflow-hidden rounded-[20px] top-[763px] w-[330px]"
          data-name="PORTRAIT"
          style={{ left: "calc(41.667% + 12px)" }}
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <div
            className="absolute bg-center bg-cover bg-no-repeat h-[556px] left-[-25px] top-[-19px] w-[355px]"
            data-name="IMAGE"
            style={{ backgroundImage: `url('${imgImage}')` }}
          />
        </motion.div>

        {/* Work Section */}
        <motion.div
          id="our-work"
          className="absolute bg-[#fadcd9] h-[726px] overflow-hidden rounded-[20px] top-[763px] w-[447px]"
          data-name="WORK"
          style={{ left: "calc(66.667% + 6px)" }}
          initial={{ x: 50, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          {/* Work Title */}
          <motion.div
            className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-6"
            initial={{ y: 10, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <p className="block leading-[normal] whitespace-pre">Musea</p>
          </motion.div>

          {/* Main Project Image */}
          <motion.div
            className="absolute h-[269px] left-6 overflow-hidden rounded-[12px] top-[60px] w-[399px]"
            initial={{ scale: 0.95, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            whileHover={{ scale: 1.02 }}
            viewport={{ once: true }}
          >
            <div
              className="h-[400px] w-[399px] bg-center bg-cover bg-no-repeat"
              style={{
                backgroundImage: `url('${imgLilcodermanPinkChairSittingOnTableInARoomInTheStyle8F5E5Aa938F84Af089F38572B0Ae93621}')`,
                backgroundPosition: 'center -113px'
              }}
            />
          </motion.div>

          {/* Other Projects */}
          <motion.div
            className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-[360px]"
            initial={{ x: -10, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            whileHover={{ color: "#feb273" }}
            viewport={{ once: true }}
          >
            <p className="block leading-[normal] whitespace-pre cursor-pointer">Elara</p>
          </motion.div>

          <motion.div
            className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-[420px]"
            initial={{ x: -10, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            whileHover={{ color: "#feb273" }}
            viewport={{ once: true }}
          >
            <p className="block leading-[normal] whitespace-pre cursor-pointer">Verve</p>
          </motion.div>

          <motion.div
            className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-[480px]"
            initial={{ x: -10, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 1.0 }}
            whileHover={{ color: "#feb273" }}
            viewport={{ once: true }}
          >
            <p className="block leading-[normal] whitespace-pre cursor-pointer">Zephyr</p>
          </motion.div>

          {/* Updated Social Frame - With Instagram, LinkedIn, Twitter */}
          <motion.div
            className="absolute bottom-6 left-6 right-6"
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 1.2 }}
            viewport={{ once: true }}
          >
            <div className="bg-[rgba(255,255,255,0.2)] backdrop-blur-sm rounded-[16px] p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <p className="text-[#000000] font-['Lufga:Medium',_sans-serif] text-[14px] mb-1">Social Projects</p>
                  <p className="text-[#666666] font-['Lufga:Light',_sans-serif] text-[11px]">Follow our journey</p>
                </div>
              </div>
              
              {/* Social Media Icons Row */}
              <div className="flex items-center gap-3">
                <SocialMediaButton 
                  icon={<InstagramIcon />} 
                  platform="instagram" 
                  delay={0.1}
                />
                <SocialMediaButton 
                  icon={<LinkedInIcon />} 
                  platform="linkedin" 
                  delay={0.2}
                />
                <SocialMediaButton 
                  icon={<TwitterIcon />} 
                  platform="twitter" 
                  delay={0.3}
                />
                
                <motion.div
                  className="ml-auto text-[#feb273] cursor-pointer font-['Lufga:Medium',_sans-serif] text-[12px]"
                  whileHover={{ x: 3 }}
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  Connect →
                </motion.div>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* About */}
        <motion.div
          className="absolute bg-[#fadcd9] h-[351px] left-[22px] overflow-hidden rounded-[20px] top-[1263px] w-[448px]"
          data-name="ABOUT"
          initial={{ scale: 0.9, opacity: 0 }}
          whileInView={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <div
            className="absolute font-['Gilroy:Light',_sans-serif] leading-[25px] left-6 not-italic text-[#000000] text-[20px] text-left w-[296px]"
            style={{ top: "calc(50% - 20.5px)" }}
          >
            <p>
              Julia Huang is an innovative AI artist, renowned for blending
              cutting-edge technology with creative expression. Based in LA, she
              crafts unique digital art experiences accessible globally.
            </p>
          </div>
          
          <div className="absolute left-6 size-[38px] top-8" data-name="CIRCLE ICON">
            <svg
              className="block size-full"
              fill="none"
              preserveAspectRatio="none"
              viewBox="0 0 38 38"
            >
              <g clipPath="url(#clip0_1_396)" id="CIRCLE ICON">
                <motion.path
                  d={svgPaths.p21c04900}
                  fill="var(--fill-0, #F8AFA6)"
                  id="Vector"
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                />
              </g>
              <defs>
                <clipPath id="clip0_1_396">
                  <rect fill="white" height="38" width="38" />
                </clipPath>
              </defs>
            </svg>
          </div>
        </motion.div>

        {/* Contact Section - Fixed with rounded corners */}
        <section id="contact" className="absolute contents">
          <motion.div
            className="absolute bg-[#f8afa6] h-[351px] overflow-hidden rounded-[20px] top-[1263px] w-[448px]"
            data-name="CONTACT"
            style={{ left: "calc(33.333% + 14px)" }}
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6 }}
            whileHover={{ scale: 1.02, backgroundColor: "#f6a194" }}
            viewport={{ once: true }}
          >
            <motion.div 
              className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[55px] text-left text-nowrap top-[259px]"
              whileHover={{ color: "#ffffff" }}
              transition={{ duration: 0.3 }}
            >
              <p className="block leading-[normal] whitespace-pre">Contact me</p>
            </motion.div>
            
            <div className="absolute box-border content-stretch flex flex-row items-end justify-between left-1/2 p-0 top-[30px] translate-x-[-50%] w-[400px]">
              <div className="font-['Gilroy:Light',_sans-serif] leading-[normal] not-italic relative shrink-0 text-[#000000] text-[15px] text-left text-nowrap whitespace-pre">
                <p className="block mb-0">Have some</p>
                <p className="block">questions?</p>
              </div>
              <motion.div
                className="relative shrink-0 size-[38px] cursor-pointer"
                whileHover={{ scale: 1.2, rotate: 45 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg
                  className="block size-full"
                  fill="none"
                  preserveAspectRatio="none"
                  viewBox="0 0 38 38"
                >
                  <g id="ARROW">
                    <path
                      clipRule="evenodd"
                      d={svgPaths.p17200c00}
                      fill="var(--fill-0, black)"
                      fillRule="evenodd"
                      id="Vector"
                    />
                  </g>
                </svg>
              </motion.div>
            </div>
          </motion.div>
        </section>

        <motion.div 
          className="absolute font-['Inter:Black',_sans-serif] font-black h-[77px] leading-[77px] left-[46px] not-italic text-[#131d26] text-[78px] text-left top-[1316px] tracking-[1.56px] w-[430px]"
          initial={{ x: -100, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 1 }}
          viewport={{ once: true }}
        >
          <p className="adjustLetterSpacing">PER PIXEL</p>
        </motion.div>
      </section>

      {/* Portfolio Section - Fixed with proper lines, 2025, and repositioned blog */}
      <motion.section 
        className="absolute contents left-[-40px] top-[1658px]"
        data-name="Section 3"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 1 }}
        viewport={{ once: true }}
      >
        {/* Background */}
        <motion.div
          className="absolute bg-[#feb273] h-[830px] rounded-[68px] top-[1658px] w-[913px]"
          data-name="bg"
          style={{ left: "calc(33.333% + 20px)" }}
          whileHover={{ backgroundColor: "#fd9f4f" }}
          transition={{ duration: 0.3 }}
        />

        {/* Year 2025 */}
        <motion.div
          className="absolute font-['Playfair_Display:Bold',_sans-serif] font-bold leading-[0] text-[#ffffff] text-[180px] text-left text-nowrap top-[1780px]"
          style={{ left: "calc(41.667% + 40px)" }}
          initial={{ scale: 0 }}
          whileInView={{ scale: 1 }}
          transition={{ duration: 1, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <p className="block leading-[normal] whitespace-pre opacity-20">2025</p>
        </motion.div>

        {/* Decorative Lines - Multiple animated lines */}
        <motion.div
          className="absolute top-[1720px] h-[1px] bg-[rgba(0,0,0,0.2)]"
          style={{ left: "calc(33.333% + 60px)", width: "calc(55% - 120px)" }}
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1.5, delay: 0.3 }}
          viewport={{ once: true }}
        />
        
        <motion.div
          className="absolute top-[1950px] h-[1px] bg-[rgba(0,0,0,0.15)]"
          style={{ left: "calc(45% + 40px)", width: "calc(35% - 80px)" }}
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1.2, delay: 0.5 }}
          viewport={{ once: true }}
        />

        <motion.div
          className="absolute top-[2180px] h-[1px] bg-[rgba(0,0,0,0.2)]"
          style={{ left: "calc(38% + 20px)", width: "calc(50% - 100px)" }}
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1.8, delay: 0.7 }}
          viewport={{ once: true }}
        />

        <motion.div
          className="absolute top-[2350px] h-[1px] bg-[rgba(0,0,0,0.1)]"
          style={{ left: "calc(42% + 60px)", width: "calc(40% - 120px)" }}
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1.3, delay: 0.9 }}
          viewport={{ once: true }}
        />
        
        <motion.div
          className="absolute font-['Glegoo:Bold',_sans-serif] leading-[120px] not-italic text-[#ffffff] text-[120px] text-left text-nowrap top-[2073px]"
          style={{ left: "calc(50% + 22px)" }}
          animate={{ x: [0, 10, 0] }}
          transition={{ duration: 4, repeat: Infinity }}
        >
          <p className="whitespace-pre">PORTFOLIO</p>
        </motion.div>

        <motion.div
          className="absolute flex h-[168.995px] items-center justify-center top-[1989px] w-[122.997px]"
          style={{ left: "calc(16.667% + 40px)" }}
          animate={{ rotate: [0, 10, -10, 0] }}
          transition={{ duration: 3, repeat: Infinity }}
        >
          <div className="flex-none rotate-[53.952deg]">
            <div className="h-0 relative w-[209.022px]">
              <div className="absolute bottom-[-117.823px] left-[-7.655%] right-[-7.655%] top-[-117.823px]">
                <svg
                  className="block size-full"
                  fill="none"
                  preserveAspectRatio="none"
                  viewBox="0 0 242 236"
                >
                  <path
                    d={svgPaths.p31536a40}
                    fill="var(--stroke-0, black)"
                    id="Arrow 1"
                  />
                </svg>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.section>

      {/* Latest Insights - Repositioned outside orange section */}
      <motion.section
        id="blog"
        className="absolute left-[22px] top-[2550px]"
        initial={{ y: 50, opacity: 0 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <motion.div
          className="bg-[#131d26] rounded-[20px] p-8 w-[500px]"
          whileHover={{ scale: 1.02 }}
        >
          <h3 className="font-['Gilroy:Bold',_sans-serif] text-[36px] text-[#ffffff] mb-6">
            Latest Insights
          </h3>
          <div className="space-y-6">
            <motion.div
              className="bg-[rgba(255,255,255,0.1)] rounded-[12px] p-6 cursor-pointer"
              whileHover={{ backgroundColor: "rgba(255,255,255,0.15)" }}
            >
              <h4 className="font-['Lufga:Medium',_sans-serif] text-[20px] text-[#ffffff] mb-3">
                AI in Creative Design
              </h4>
              <p className="font-['Lufga:Light',_sans-serif] text-[16px] text-[rgba(255,255,255,0.8)] leading-[24px]">
                Exploring how artificial intelligence is reshaping the creative landscape and opening new possibilities...
              </p>
              <motion.div
                className="mt-4 text-[#feb273] font-['Lufga:Medium',_sans-serif] text-[14px]"
                whileHover={{ x: 5 }}
              >
                Read more →
              </motion.div>
            </motion.div>
            <motion.div
              className="bg-[rgba(255,255,255,0.1)] rounded-[12px] p-6 cursor-pointer"
              whileHover={{ backgroundColor: "rgba(255,255,255,0.15)" }}
            >
              <h4 className="font-['Lufga:Medium',_sans-serif] text-[20px] text-[#ffffff] mb-3">
                Future of Digital Art
              </h4>
              <p className="font-['Lufga:Light',_sans-serif] text-[16px] text-[rgba(255,255,255,0.8)] leading-[24px]">
                A deep dive into emerging trends and technologies that are defining the future of digital art...
              </p>
              <motion.div
                className="mt-4 text-[#feb273] font-['Lufga:Medium',_sans-serif] text-[14px]"
                whileHover={{ x: 5 }}
              >
                Read more →
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </motion.section>

      {/* Testimonials Section - Completely redone */}
      <section id="testimonials" className="absolute top-[2880px] w-full left-[22px]">
        <motion.div
          className="absolute font-['Playfair_Display:Regular',_sans-serif] font-normal leading-[81px] text-[#000000] text-[81.308px] text-left text-nowrap top-0"
          style={{ left: "calc(20% + 50px)" }}
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <p className="whitespace-pre">Testimonials</p>
        </motion.div>

        {/* First testimonial card */}
        <motion.div
          className="absolute top-[150px] left-0"
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <div className="bg-[#f9cec9] rounded-[20px] w-[400px] overflow-hidden cursor-pointer">
            <motion.div
              className="flex h-[280px] items-center justify-center w-full"
              whileHover={{ scale: 1.05 }}
            >
              <div className="flex-none rotate-[270deg]">
                <div
                  className="h-[300px] w-[280px] bg-center bg-cover bg-no-repeat"
                  style={{ backgroundImage: `url('${imgRectangle31}')` }}
                />
              </div>
            </motion.div>
            
            <div className="p-6">
              <motion.h3 
                className="font-['Inter:Semi_Bold',_sans-serif] font-semibold text-[20px] text-[#000000] mb-3"
                whileHover={{ scale: 1.05 }}
              >
                "Amazing Team with Great Results"
              </motion.h3>
              
              <p className="font-['Inter:Light',_sans-serif] font-light text-[14px] text-[#000000] mb-4 leading-[20px]">
                Working with Julia was an incredible experience. Her attention to detail and creative vision transformed our project beyond expectations.
              </p>
              
              <motion.button
                className="font-['Inter:Medium',_sans-serif] font-medium text-[14px] text-[#000000] hover:text-[#feb273] transition-colors duration-300"
                whileHover={{ x: 5 }}
              >
                See full review →
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Second testimonial card */}
        <motion.div
          className="absolute top-[150px]"
          style={{ left: "calc(50% - 100px)" }}
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <div className="bg-[#181818] rounded-[20px] w-[400px] overflow-hidden cursor-pointer">
            <motion.div
              className="flex h-[280px] items-center justify-center w-full"
              whileHover={{ scale: 1.05 }}
            >
              <div className="flex-none rotate-[270deg]">
                <div
                  className="h-[300px] w-[280px] bg-center bg-cover bg-no-repeat"
                  style={{ backgroundImage: `url('${imgRectangle28}')` }}
                />
              </div>
            </motion.div>
            
            <div className="p-6">
              <motion.h3 
                className="font-['Inter:Semi_Bold',_sans-serif] font-semibold text-[20px] text-[#ffffff] mb-3"
                whileHover={{ scale: 1.05 }}
              >
                "Innovative Design Solutions"
              </motion.h3>
              
              <p className="font-['Inter:Light',_sans-serif] font-light text-[14px] text-[#ffffff] mb-4 leading-[20px]">
                Julia's innovative approach to design and her ability to integrate AI technology seamlessly impressed our entire team.
              </p>
              
              <motion.button
                className="font-['Inter:Medium',_sans-serif] font-medium text-[14px] text-[#ffffff] hover:text-[#feb273] transition-colors duration-300"
                whileHover={{ x: 5 }}
              >
                See full review →
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Third testimonial card */}
        <motion.div
          className="absolute top-[500px] left-[200px]"
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <div className="bg-[#181818] rounded-[20px] w-[400px] overflow-hidden cursor-pointer">
            <motion.div
              className="flex h-[280px] items-center justify-center w-full"
              whileHover={{ scale: 1.05 }}
            >
              <div className="flex-none rotate-[270deg]">
                <div
                  className="h-[300px] w-[280px] bg-center bg-cover bg-no-repeat"
                  style={{ backgroundImage: `url('${imgRectangle26}')` }}
                />
              </div>
            </motion.div>
            
            <div className="p-6">
              <motion.h3 
                className="font-['Inter:Semi_Bold',_sans-serif] font-semibold text-[20px] text-[#ffffff] mb-3"
                whileHover={{ scale: 1.05 }}
              >
                "Exceptional Creative Vision"
              </motion.h3>
              
              <p className="font-['Inter:Light',_sans-serif] font-light text-[14px] text-[#ffffff] mb-4 leading-[20px]">
                The creative vision and technical expertise Julia brought to our project resulted in something truly extraordinary.
              </p>
              
              <motion.button
                className="font-['Inter:Medium',_sans-serif] font-medium text-[14px] text-[#ffffff] hover:text-[#feb273] transition-colors duration-300"
                whileHover={{ x: 5 }}
              >
                See full review →
              </motion.button>
            </div>
          </div>
        </motion.div>
      </section>
    </div>
  );
}

export default {
p12a08980: "M118.104 69.6866C117.594 72.5391 111.498 74.3809 102.562 74.3809H102.336C98.9952 71.6607 94.9377 68.8932 90.3706 66.2958C89.0589 65.5496 87.7473 64.8412 86.4357 64.1801C87.9077 63.9912 89.3798 63.7739 90.8612 63.5189C95.9096 62.6405 100.684 61.4126 104.865 59.9581C113.206 62.9144 118.641 66.6547 118.094 69.6772L118.104 69.6866ZM111.036 88.832C110.215 90.2299 108.045 90.5227 106.365 90.5227C103.279 90.5227 99.1651 89.5877 94.5885 87.9253C92.8145 84.9501 90.6065 81.7954 88.0398 78.584C87.9549 78.4801 87.8605 78.3668 87.7756 78.2629C87.5586 77.989 87.3415 77.7245 87.1151 77.4506C86.7942 77.0633 86.4734 76.6761 86.1431 76.2983C85.1712 75.1554 84.1804 74.0503 83.1896 72.9925C84.6145 73.3041 86.0677 73.5969 87.568 73.852C92.5409 74.7115 97.4949 75.1837 101.958 75.2215C108.771 80.8131 112.583 86.1685 111.026 88.8226L111.036 88.832ZM79.8586 114.617C79.5944 114.712 79.3019 114.759 78.9999 114.759C76.0181 114.759 71.819 110.301 67.8463 103.472C68.5635 99.269 68.9598 94.433 68.9598 89.2476C68.9598 87.7647 68.9221 86.2913 68.856 84.8462C69.743 85.989 70.6678 87.1319 71.6491 88.2842C74.0648 91.1178 76.5559 93.7247 79.0188 96.0009C79.953 96.8605 80.8683 97.6822 81.7836 98.4473C83.35 107.052 82.7838 113.569 79.8586 114.617ZM59.5047 118.159C56.3813 118.159 53.5882 112.228 52.0784 103.604C54.2299 99.8735 56.2964 95.4626 58.0704 90.6455C58.5894 89.2382 59.0612 87.8497 59.5047 86.4802C59.9388 87.8497 60.42 89.2382 60.939 90.6455C62.713 95.4531 64.7796 99.864 66.931 103.604C65.4212 112.228 62.6281 118.159 59.5047 118.159ZM40.0095 114.769C39.7076 114.769 39.415 114.721 39.1508 114.627C36.2162 113.578 35.65 107.061 37.2258 98.4567C40.5568 95.6609 44.0293 92.1945 47.3509 88.2937C48.3323 87.1508 49.257 85.9985 50.144 84.8556C50.078 86.2913 50.0402 87.7553 50.0402 89.2571C50.0402 94.4425 50.446 99.2784 51.1537 103.482C47.1905 110.31 42.9819 114.769 40.0095 114.769ZM23.2791 105.068C22.4299 105.068 21.7505 104.861 21.2503 104.445C19.5801 103.066 19.8821 99.3729 22.0807 94.3103C22.8639 92.5062 23.8547 90.5888 25.0248 88.6148C29.1485 87.0941 33.6306 85.0067 38.094 82.466C39.4056 81.7198 40.6795 80.9548 41.9156 80.1802C41.3495 81.512 40.8116 82.8721 40.2926 84.2795C38.4903 89.1721 37.1881 93.8663 36.3955 98.0694C31.1678 102.433 26.3931 105.068 23.2886 105.068H23.2791ZM12.6351 90.5322C10.9554 90.5322 8.78512 90.2394 7.96417 88.8415C6.40719 86.1779 10.2194 80.832 17.0324 75.2404C21.4863 75.2026 26.4403 74.7304 31.4226 73.8709C32.9229 73.6064 34.3855 73.3136 35.8104 73.0113C34.8196 74.0692 33.8288 75.1743 32.8569 76.3172C29.5542 80.1991 26.6856 84.1756 24.4303 87.9442C19.8444 89.616 15.7302 90.5511 12.6351 90.5511V90.5322ZM16.4379 74.3903C7.51123 74.3903 1.41544 72.5485 0.89645 69.6961C0.358586 66.6736 5.7844 62.9333 14.1166 59.977C18.193 61.4032 22.9489 62.6311 28.1293 63.5284C29.6108 63.7834 31.0923 64.0101 32.5549 64.199C31.2433 64.8601 29.9411 65.5685 28.62 66.3147C24.0529 68.9121 20.0048 71.6796 16.6644 74.3998H16.4284L16.4379 74.3903ZM7.96417 30.1774C8.78512 28.7795 10.9554 28.4867 12.6351 28.4867C15.7207 28.4867 19.8349 29.4218 24.4115 31.0841C26.6101 34.7583 29.4598 38.7253 32.8474 42.7112C33.8194 43.854 34.8102 44.9591 35.801 46.017C34.3761 45.7053 32.9135 45.4125 31.4131 45.1575C28.8182 44.7041 26.2232 44.3641 23.7038 44.1279C21.4013 43.9202 19.1555 43.8068 17.0229 43.7879C10.21 38.1964 6.39776 32.8504 7.95473 30.1868L7.96417 30.1774ZM21.2503 14.5739C21.7505 14.1583 22.4299 13.9506 23.2791 13.9506C26.3836 13.9506 31.1678 16.5952 36.3955 20.9494C37.1881 25.1526 38.4809 29.8468 40.2926 34.73C40.8116 36.1373 41.3589 37.5069 41.9156 38.8386C40.6889 38.0641 39.415 37.2991 38.094 36.5529C33.6401 34.0121 29.1673 31.9342 25.0437 30.4135C20.5804 22.8574 18.8536 16.548 21.2409 14.5739H21.2503ZM39.1508 4.39202C39.415 4.29756 39.7076 4.25034 40.0095 4.25034C42.9819 4.25034 47.1905 8.70847 51.1537 15.5279C50.4365 19.731 50.0402 24.5669 50.0402 29.7618C50.0402 30.2624 50.0402 30.763 50.0496 31.2636C50.0496 31.6131 50.0591 31.9625 50.078 32.312C50.078 32.4442 50.078 32.5859 50.0874 32.7181C50.0968 33.2093 50.1251 33.691 50.144 34.1633C49.257 33.0204 48.3323 31.8775 47.3509 30.7252C44.0293 26.8243 40.5568 23.358 37.2258 20.5622C35.65 11.9576 36.2162 5.44988 39.1508 4.39202ZM78.9999 4.25034C79.3019 4.25034 79.585 4.29756 79.8586 4.39202C81.8968 5.1193 82.8971 8.69902 82.5951 14.1961C82.4913 16.1607 82.2177 18.2953 81.8119 20.5433C78.4809 23.3485 74.9895 26.8149 71.6586 30.7252C70.6866 31.8681 69.7619 33.0109 68.8654 34.1633C68.9315 32.7276 68.9692 31.2636 68.9692 29.7618C68.9692 29.2423 68.9692 28.7323 68.9598 28.2128C68.9598 28.1561 68.9598 28.0995 68.9598 28.0428C68.9598 27.6177 68.9409 27.1927 68.9315 26.7677C68.9221 26.3993 68.9032 26.0498 68.8938 25.6909C68.8938 25.5776 68.8938 25.4642 68.8843 25.3603C68.7239 21.8278 68.3748 18.522 67.8652 15.5279C71.8378 8.69902 76.037 4.25034 79.0188 4.25034H78.9999ZM95.7303 13.9506C96.5701 13.9506 97.259 14.1678 97.7591 14.5739C100.146 16.548 98.4291 22.8574 93.9658 30.4135C89.8421 31.9342 85.3788 34.0121 80.9249 36.5529C79.6038 37.2991 78.33 38.0641 77.1033 38.8386C77.6694 37.5069 78.2073 36.1373 78.7263 34.73C80.4814 29.9696 81.8025 25.2564 82.6046 20.9589C87.8322 16.5858 92.6258 13.9411 95.7303 13.9411V13.9506ZM106.374 28.4867C108.054 28.4867 110.224 28.7795 111.045 30.1774C112.13 32.0381 110.564 35.4006 106.752 39.4242C105.393 40.8599 103.798 42.3239 102.015 43.7785C97.5515 43.8163 92.5786 44.2885 87.5963 45.148C86.096 45.403 84.6333 45.6958 83.2179 46.0075C84.2087 44.9497 85.1901 43.8446 86.1714 42.7017C89.559 38.7253 92.4088 34.7583 94.6074 31.0747C99.184 29.4123 103.298 28.4773 106.384 28.4773L106.374 28.4867ZM75.4614 59.5047C78.4904 58.4941 81.7459 57.1434 85.0768 55.5094C86.9358 55.7266 88.8325 55.9911 90.7291 56.3217C95.3717 57.1245 99.7407 58.2391 103.572 59.5142C99.6935 60.8082 95.3246 61.8944 90.7291 62.6878C88.8325 63.0183 86.9452 63.2828 85.0768 63.5C81.7364 61.866 78.4809 60.5154 75.4519 59.5047H75.4614ZM75.8954 64.0573C75.0179 64.0573 74.1686 64.0384 73.3476 64.0006C71.8945 62.905 70.479 61.9605 69.1485 61.1765C70.6866 60.8932 72.338 60.4776 74.0931 59.9392C77.0089 60.8554 80.1889 62.1116 83.4633 63.6606C80.8305 63.9156 78.2828 64.0479 75.8954 64.0479V64.0573ZM74.4989 64.8885C74.9612 64.8979 75.4142 64.9074 75.8954 64.9074C78.7263 64.9074 81.7836 64.7279 84.9259 64.3784C86.5866 65.2002 88.2757 66.088 89.9459 67.0514C94.0318 69.3749 97.7497 71.8874 100.901 74.3809C96.7777 74.2864 92.2672 73.8236 87.7379 73.0397C85.8223 72.7091 83.9539 72.3218 82.1422 71.8968C79.5661 69.2333 76.9711 66.872 74.4894 64.9074L74.4989 64.8885ZM93.258 87.4342C89.4741 85.9702 85.4071 84.0339 81.3495 81.7293C79.6605 80.7753 78.0374 79.7741 76.4899 78.7635C74.9895 75.3821 73.376 72.303 71.7246 69.63C74.6687 70.7539 78.0374 71.7835 81.7176 72.6524C82.9914 73.9842 84.2559 75.3821 85.5015 76.8461C88.5305 80.4069 91.1538 84.0244 93.258 87.4247V87.4342ZM72.3191 87.7458C71.0735 86.2818 69.894 84.7989 68.7805 83.3255C68.554 79.6985 68.1105 76.251 67.4878 73.153C69.8562 75.2026 72.6588 77.2995 75.7916 79.3396C76.5371 81.0209 77.2354 82.7682 77.9053 84.5628C79.5189 88.9359 80.7362 93.2241 81.5288 97.1344C81.2741 96.9171 81.0287 96.6904 80.7645 96.4638C77.9714 93.9608 75.0933 91.0139 72.3097 87.7458H72.3191ZM57.4382 73.7292C58.278 72.1235 58.9763 70.6028 59.5142 69.1766C60.052 70.6028 60.7503 72.1235 61.5901 73.7292C61.1844 76.7044 60.4955 80.0008 59.5142 83.4672C58.5422 80.0008 57.844 76.695 57.4382 73.7292ZM52.6352 72.1613C53.5222 71.3584 54.3431 70.5461 55.098 69.7528C55.4755 69.3561 55.8246 68.9688 56.1549 68.591C56.1643 70.1117 56.2964 71.7835 56.5229 73.5686C55.1075 76.2227 53.3146 79.094 51.1914 82.0221C51.4745 78.4329 51.9746 75.0893 52.6257 72.1613H52.6352ZM51.5217 73.1436C50.9083 76.1944 50.4648 79.623 50.2384 83.3349C49.1343 84.7989 47.9548 86.2818 46.6998 87.7458C43.6708 91.3067 40.5191 94.4992 37.4901 97.1344C38.2827 93.2241 39.4905 88.9359 41.1041 84.5628C41.7741 82.7588 42.4724 81.0114 43.2178 79.3396C46.294 77.3278 49.106 75.231 51.5123 73.153L51.5217 73.1436ZM48.6531 69.0916C50.3422 68.4021 51.8708 67.6654 53.2108 66.9192C52.6918 68.3549 52.2294 69.9794 51.8237 71.7457C49.5401 73.7953 46.8508 75.8732 43.8878 77.8756C45.3976 74.6076 47.0206 71.6323 48.6531 69.0916ZM47.3037 69.6205C45.6524 72.2935 44.0294 75.3821 42.5384 78.7635C40.9815 79.7741 39.3584 80.7658 37.6788 81.7198C33.6118 84.0339 29.5353 85.9702 25.742 87.4342C27.884 83.9772 30.5261 80.3692 33.5268 76.8461C34.7819 75.3821 36.0463 73.9842 37.3108 72.6524C40.9909 71.7835 44.3596 70.7539 47.3037 69.6205ZM45.9449 64.8413C46.1619 64.8318 46.3695 64.8224 46.5866 64.8129C47.2754 64.7751 47.9454 64.7185 48.587 64.6618C49.4552 64.5768 50.2856 64.4729 51.0688 64.3407C50.0874 65.5119 49.0966 66.8625 48.1152 68.3738C45.2844 69.5166 42.01 70.5839 38.4526 71.5001C41.0003 68.931 43.5481 66.6831 45.9543 64.8507L45.9449 64.8413ZM43.1329 64.9074C43.6047 64.9074 44.0671 64.9074 44.5295 64.8885C42.0477 66.8531 39.4622 69.2144 36.8861 71.8779C35.0744 72.303 33.206 72.6902 31.2905 73.0208C26.7611 73.8047 22.2411 74.2676 18.1175 74.3715C21.2787 71.8779 24.9871 69.3655 29.073 67.042C30.7526 66.088 32.4323 65.1907 34.093 64.369C37.2353 64.7185 40.2926 64.8979 43.1235 64.8979L43.1329 64.9074ZM15.4848 59.5142C19.3065 58.2391 23.666 57.134 28.2992 56.3311C30.1959 56.0006 32.0831 55.7266 33.942 55.5188C37.2825 57.1529 40.538 58.4941 43.567 59.5142C40.538 60.5248 37.2825 61.8755 33.942 63.5095C32.0831 63.2922 30.1959 63.0278 28.2992 62.6972C23.666 61.8943 19.3065 60.7893 15.4848 59.5142ZM16.4568 44.6285C16.5322 44.6285 16.6172 44.6285 16.6927 44.6285C20.0331 47.3488 24.0906 50.1068 28.6483 52.7042C29.96 53.4503 31.2716 54.1493 32.5832 54.8199C31.1206 55.0088 29.6391 55.226 28.1576 55.4811C22.9772 56.3784 18.2213 57.6157 14.1449 59.0325C5.7844 56.0761 0.34915 52.3453 0.89645 49.3134C1.02856 48.5672 1.54755 47.8966 2.38737 47.3015C4.77473 45.6203 9.83254 44.6096 16.4379 44.6096L16.4568 44.6285ZM31.2905 45.9981C33.206 46.3287 35.0744 46.7159 36.8861 47.141C39.4622 49.8139 42.0477 52.1658 44.5295 54.1304C44.0765 54.121 43.6142 54.1115 43.1329 54.1115C40.2926 54.1115 37.2447 54.291 34.1025 54.6404C32.4417 53.8187 30.762 52.9309 29.0824 51.9675C24.9965 49.6439 21.2787 47.1315 18.127 44.638C19.9104 44.6758 21.7599 44.7891 23.6566 44.9686C26.1477 45.1953 28.7238 45.5447 31.2905 45.9886V45.9981ZM47.3037 49.3984C44.3596 48.2649 40.9909 47.2354 37.3108 46.3665C36.0369 45.0441 34.7724 43.6368 33.5268 42.1728C30.4978 38.612 27.8746 34.9944 25.7703 31.5942C29.5542 33.0582 33.6212 34.9944 37.6788 37.2991C39.3679 38.2625 40.9909 39.2542 42.5384 40.2554C44.0388 43.6368 45.6524 46.7254 47.3037 49.3984ZM51.8331 47.311C52.2388 49.0678 52.7012 50.6735 53.2108 52.1091C51.8614 51.363 50.3422 50.6357 48.6531 49.9367C47.0206 47.4054 45.407 44.4302 43.8972 41.1622C46.9074 43.2023 49.5873 45.2897 51.8331 47.311ZM51.5217 45.8659C49.1532 43.8257 46.3601 41.7289 43.2178 39.6887C42.4818 38.0075 41.7741 36.2601 41.1041 34.4655C39.4905 30.0924 38.2733 25.8043 37.4901 21.894C40.5191 24.5292 43.6708 27.7216 46.6998 31.2825C47.9454 32.7465 49.1249 34.2294 50.2384 35.6934C50.4648 39.3959 50.9083 42.8245 51.5217 45.8659ZM51.8708 16.7747C53.8053 20.2788 55.6736 24.3214 57.2778 28.6662C57.9478 30.4702 58.5328 32.2553 59.0612 34.0027C57.9949 37.5541 57.2212 40.926 56.7399 43.9957C55.2207 41.2755 53.324 38.3664 51.0876 35.3911C50.9744 33.5682 50.9178 31.6886 50.9178 29.7712C50.9178 25.1337 51.2669 20.7039 51.8708 16.7747ZM67.5161 45.8659C68.1294 42.8245 68.5729 39.3865 68.7994 35.6839C69.9034 34.2105 71.0924 32.737 72.3285 31.2825C75.367 27.7122 78.5281 24.5197 81.5666 21.8751C80.7645 25.842 79.5284 30.1302 77.9242 34.4655C77.2637 36.2601 76.5559 38.0075 75.8105 39.6887C72.6777 41.7289 69.8846 43.8257 67.5161 45.8659ZM70.3847 49.9367C68.6956 50.6357 67.1669 51.363 65.8175 52.1091C65.9025 51.8636 65.9968 51.618 66.0723 51.363C66.4875 50.1256 66.865 48.7655 67.1952 47.311C69.4411 45.2897 72.1304 43.2023 75.1311 41.1622C73.6213 44.4207 71.9983 47.396 70.3752 49.9273L70.3847 49.9367ZM71.7246 49.4078C73.376 46.7348 74.9895 43.6557 76.4899 40.2743C78.0374 39.2637 79.6605 38.2719 81.3495 37.3085C85.4071 35.0039 89.4741 33.0676 93.258 31.6036C91.1538 35.0039 88.5305 38.6214 85.5015 42.1822C84.2559 43.6462 82.982 45.0536 81.7176 46.3759C78.0374 47.2449 74.6687 48.2744 71.7246 49.4078ZM73.0929 54.1871C71.2151 54.2721 69.4977 54.4421 67.969 54.6877C68.9504 53.5165 69.9412 52.1658 70.9225 50.6546C73.7534 49.5023 77.0278 48.435 80.5852 47.5282C78.0374 50.0973 75.4897 52.3453 73.0834 54.1776L73.0929 54.1871ZM75.8954 54.121C75.4236 54.121 74.9612 54.121 74.4989 54.1398C76.9806 52.1752 79.5661 49.8234 82.1516 47.1504C83.9539 46.7254 85.8223 46.3381 87.7473 46.0075C92.2861 45.2141 96.8061 44.7608 100.949 44.6569C97.7402 47.1787 94.0035 49.6817 89.9554 51.9863C88.2757 52.9403 86.5961 53.8376 84.9353 54.6593C81.793 54.3099 78.7452 54.1304 75.9049 54.1304L75.8954 54.121ZM69.1485 57.8424C70.479 57.0679 71.8945 56.1139 73.3476 55.0183C74.1686 54.9899 75.0178 54.971 75.886 54.971C78.2733 54.971 80.8305 55.1033 83.4633 55.3583C80.1795 56.9073 77.0089 58.1635 74.0931 59.0891C72.3474 58.5508 70.6866 58.1352 69.1485 57.8518V57.8424ZM65.7515 59.5236C66.4592 59.2497 67.1952 58.9097 67.9596 58.5035C69.3939 58.7113 70.9508 59.0608 72.6116 59.5236C70.9508 59.9864 69.3939 60.3264 67.9596 60.5437C67.1952 60.1375 66.4498 59.7975 65.7515 59.5236ZM67.969 64.3501C69.4977 64.6051 71.2151 64.7751 73.0929 64.8507C75.4991 66.6831 78.0374 68.931 80.5947 71.5001C77.0372 70.5934 73.7628 69.5261 70.932 68.3738C69.9506 66.8625 68.9598 65.5119 67.9784 64.3407L67.969 64.3501ZM65.3269 65.663C64.9966 64.8601 64.6475 64.1423 64.2983 63.4811C65.0155 63.7173 65.7987 63.9251 66.6574 64.1045C67.5916 65.1529 68.5446 66.3808 69.4977 67.7693C67.9407 67.0798 66.5441 66.3714 65.3363 65.6535L65.3269 65.663ZM66.3082 68.4115C66.1478 67.8921 65.978 67.4009 65.8175 66.9286C67.1575 67.6748 68.6956 68.4115 70.3847 69.1105C72.0171 71.6512 73.6307 74.617 75.1405 77.8851C72.1304 75.8449 69.4411 73.7575 67.1952 71.7362C66.931 70.565 66.6291 69.4694 66.3177 68.421L66.3082 68.4115ZM62.8451 67.2498C62.8074 66.3808 62.7319 65.578 62.6187 64.8413C63.2132 65.3041 63.8831 65.7763 64.6286 66.2391C64.9305 66.9853 65.2136 67.7882 65.4778 68.6571C65.676 69.2994 65.8647 69.97 66.044 70.6595C64.8079 69.4883 63.7321 68.336 62.8451 67.2498ZM62.496 73.5875C62.7225 71.8024 62.8451 70.1306 62.864 68.6099C63.8737 69.7716 65.0626 70.9806 66.412 72.2085C67.082 75.1837 67.5538 78.5179 67.8369 82.0598C65.7137 79.1224 63.9114 76.251 62.5054 73.5875H62.496ZM57.4099 63.9629C57.6835 63.6984 57.9289 63.4434 58.1554 63.1883C58.1554 63.5189 58.1742 63.8684 58.212 64.2368C57.9194 64.8035 57.542 65.3985 57.1079 66.0219C57.174 65.2757 57.2683 64.5862 57.4099 63.9723V63.9629ZM55.985 62.9994C56.3247 62.8483 56.655 62.6877 56.9475 62.5272C56.8343 62.8389 56.7305 63.1789 56.6456 63.5378C56.1643 63.9628 55.617 64.3973 54.9848 64.8318C55.3056 64.1518 55.6359 63.5473 55.985 63.0089V62.9994ZM54.7206 63.4811C54.3526 64.1329 54.0129 64.8696 53.6826 65.663C52.4748 66.3808 51.0688 67.0892 49.5118 67.7787C50.4648 66.3903 51.4179 65.1624 52.3521 64.114C53.2108 63.9345 54.0034 63.7267 54.7206 63.4811ZM51.0499 58.5035C51.8142 58.9097 52.5597 59.2497 53.258 59.5236C52.5502 59.7975 51.8142 60.1375 51.0499 60.5437C49.6156 60.3359 48.0586 59.9959 46.3978 59.5236C48.0586 59.0608 49.6156 58.7113 51.0499 58.5035ZM49.8609 57.8424C48.3228 58.1257 46.6715 58.5413 44.9258 59.0797C42.01 58.1635 38.83 56.8978 35.5462 55.3488C38.1789 55.0938 40.7267 54.9616 43.1235 54.9616C44.001 54.9616 44.8503 54.9805 45.6618 55.0088C47.115 56.1045 48.5304 57.049 49.8609 57.8329V57.8424ZM51.9558 55.7361C52.5314 56.3689 53.107 56.9262 53.6637 57.4173C52.8805 57.4362 52.0501 57.5118 51.1631 57.6346C49.9081 56.9545 48.5682 56.1139 47.1716 55.1127C48.9362 55.2355 50.5403 55.4433 51.9558 55.7361ZM48.0869 50.664C49.0683 52.1752 50.0685 53.5259 51.0404 54.6971C49.5118 54.4421 47.7944 54.2815 45.926 54.1965C43.5198 52.3642 40.972 50.1162 38.4242 47.5471C41.9817 48.4538 45.2561 49.5212 48.0869 50.6735V50.664ZM56.1454 50.4373C55.1452 49.285 53.9657 48.0855 52.6163 46.8576C51.9652 43.9296 51.4745 40.5954 51.182 37.0063C53.2957 39.9343 55.098 42.8056 56.5134 45.4597C56.2775 47.2543 56.1549 48.9167 56.1454 50.4373ZM59.0518 51.1457C58.7781 51.9769 58.5705 52.7514 58.4196 53.4787C58.0232 52.8364 57.5609 52.1847 57.023 51.5046C56.9947 50.1162 57.0702 48.5672 57.2495 46.8954C57.9949 48.4066 58.6083 49.8423 59.0612 51.1552L59.0518 51.1457ZM61.5807 45.318C61.024 46.3759 60.5333 47.4149 60.0992 48.4161C59.8822 48.9167 59.6935 49.3984 59.5142 49.8612C58.9763 48.435 58.278 46.9143 57.4382 45.2991C57.844 42.3239 58.5328 39.0275 59.5142 35.5612C60.4955 39.0275 61.1844 42.3334 61.5901 45.3086L61.5807 45.318ZM62.2884 44.024C61.8072 40.9544 61.0334 37.573 59.9577 34.0121C60.4861 32.2648 61.0806 30.4702 61.7411 28.6756C63.3453 24.3308 65.2042 20.2883 67.1481 16.7841C67.7614 20.7133 68.1011 25.1431 68.1011 29.7807C68.1011 31.6981 68.0445 33.5777 67.9218 35.4006C65.7326 38.3286 63.8171 41.2472 62.2695 44.0146L62.2884 44.024ZM66.3932 46.867C65.0532 48.0855 63.8643 49.285 62.8734 50.4468C62.8546 48.9261 62.7319 47.2449 62.496 45.4503C63.9303 42.7489 65.7326 39.8965 67.8275 37.0063C67.5444 40.6049 67.0443 43.9485 66.3932 46.8765V46.867ZM64.6286 52.827C63.8831 53.2803 63.2132 53.7526 62.6187 54.2154C62.7319 53.4692 62.8074 52.6664 62.8451 51.8069C63.7227 50.7207 64.7984 49.5873 66.0157 48.4161C65.9685 48.605 65.9213 48.7939 65.8742 48.9733C65.4967 50.3807 65.0815 51.6747 64.6286 52.8175V52.827ZM64.2889 55.5755C64.6569 54.9238 64.9966 54.1965 65.3269 53.3937C66.5347 52.6758 67.9313 51.958 69.4882 51.278C68.5352 52.6664 67.5821 53.9037 66.6574 54.9427C65.7987 55.1221 65.006 55.3299 64.2889 55.5755ZM64.0341 57.4457C63.6567 57.474 63.2886 57.5118 62.9584 57.5685C63.166 57.3229 63.3736 57.049 63.5717 56.7656C64.1662 56.52 64.8456 56.3028 65.5816 56.095C65.0532 56.6145 64.5342 57.0679 64.0341 57.4457ZM65.3457 57.4362C65.9119 56.9451 66.4781 56.3784 67.0537 55.755C68.4691 55.4622 70.0733 55.2544 71.8378 55.1316C70.4413 56.1233 69.1013 56.9734 67.8463 57.6535C66.9593 57.5307 66.1195 57.4551 65.3457 57.4362ZM64.2983 59.0513C63.9964 58.9663 63.7038 58.9002 63.4208 58.853C63.7133 58.6924 63.9964 58.5035 64.2889 58.2863C64.5059 58.2768 64.7229 58.2768 64.9305 58.2768C65.4118 58.2768 65.9308 58.3052 66.4592 58.3524C65.7704 58.6641 65.1098 58.9191 64.487 59.108C64.4304 59.0891 64.3644 59.0702 64.3078 59.0513H64.2983ZM65.3457 61.6488C66.1289 61.6299 66.9593 61.5543 67.8463 61.4315C69.1013 62.1021 70.4413 62.9522 71.8378 63.9534C70.0733 63.8306 68.4691 63.6134 67.0537 63.33C66.4781 62.6972 65.9025 62.1305 65.3457 61.6488ZM62.9678 61.5165C63.3075 61.5732 63.6567 61.6016 64.0341 61.6299C64.5342 62.0077 65.0532 62.4611 65.5816 62.9805C64.8456 62.7822 64.1756 62.565 63.5812 62.3194C63.383 62.0266 63.1754 61.7621 62.9678 61.5165ZM62.0619 62.5461C62.3545 62.7066 62.6753 62.8672 63.015 63.0183C63.3641 63.5473 63.6944 64.1612 64.0247 64.8413C63.4019 64.4162 62.8451 63.9817 62.3733 63.5567C62.2884 63.1978 62.1846 62.8672 62.0714 62.5461H62.0619ZM60.8069 64.2745C60.8447 63.9062 60.8635 63.5473 60.873 63.2072C61.0994 63.4623 61.3354 63.7267 61.6184 63.9817C61.76 64.6051 61.8543 65.2946 61.9204 66.0408C61.4769 65.4269 61.1089 64.8318 60.8164 64.2651L60.8069 64.2745ZM54.9942 61.6299C55.3717 61.6016 55.7397 61.5638 56.0699 61.5071C55.8623 61.7527 55.6642 62.0266 55.466 62.3099C54.8715 62.5555 54.1921 62.7822 53.4561 62.9805C53.9845 62.4611 54.5035 62.0077 55.0037 61.6299H54.9942ZM53.6826 61.6488C53.1164 62.1305 52.5502 62.6972 51.9746 63.33C50.5592 63.6134 48.955 63.8212 47.1905 63.9534C48.587 62.9522 49.927 62.1021 51.182 61.4315C52.069 61.5543 52.9088 61.6299 53.6826 61.6488ZM55.6076 60.232C55.3245 60.402 55.032 60.5815 54.7394 60.7987C54.5224 60.8082 54.3054 60.8176 54.0978 60.8176C53.6165 60.8176 53.0975 60.7893 52.5691 60.742C53.258 60.4303 53.9279 60.1753 54.5413 59.977C54.9093 60.0903 55.2584 60.1659 55.6076 60.232ZM54.7394 58.2863C55.032 58.5035 55.3245 58.6924 55.6076 58.853C55.2679 58.9191 54.9093 59.0041 54.5413 59.108C53.9185 58.9191 53.258 58.6641 52.5691 58.3524C53.0975 58.3052 53.6165 58.2768 54.0978 58.2768C54.3054 58.2768 54.5224 58.2768 54.7394 58.2863ZM55.4566 56.7656C55.6548 57.0584 55.8623 57.3229 56.0699 57.5685C55.7302 57.5118 55.3811 57.474 55.0037 57.4457C54.5035 57.0679 53.9845 56.6145 53.4561 56.095C54.1921 56.2934 54.8621 56.5106 55.4566 56.7656ZM53.7015 53.4031C53.7015 53.4031 53.7109 53.4409 53.7203 53.4598C53.7486 53.5165 53.7675 53.5731 53.7958 53.6298C53.8619 53.7809 53.9185 53.9132 53.9845 54.0548C54.2299 54.6027 54.4847 55.1127 54.7394 55.5755C54.0223 55.3394 53.2296 55.1316 52.371 54.9427C51.4462 53.8943 50.4931 52.6664 49.5401 51.278C51.0971 51.9675 52.5031 52.6758 53.7109 53.3937L53.7015 53.4031ZM58.1554 55.8683C57.9383 55.6133 57.693 55.3488 57.4193 55.0938C57.2778 54.4704 57.174 53.7809 57.1079 53.0348C57.5514 53.6487 57.91 54.2343 58.212 54.801C58.1742 55.1788 58.1553 55.5283 58.1459 55.8683H58.1554ZM59.9388 54.6593C59.7689 54.9899 59.6274 55.3205 59.5142 55.6416C59.4009 55.3299 59.2594 55.0088 59.099 54.6782C59.1839 54.0454 59.3254 53.3653 59.5236 52.6286C59.7218 53.3559 59.8633 54.036 59.9482 54.6688L59.9388 54.6593ZM61.609 55.0938C61.3354 55.3583 61.09 55.6133 60.8635 55.8778C60.8635 55.5377 60.8447 55.1883 60.7975 54.8105C61.0994 54.2437 61.4675 53.6487 61.9015 53.0348C61.8355 53.7809 61.7317 54.4704 61.5996 55.0938H61.609ZM63.0339 56.0572C62.6847 56.2083 62.3639 56.3689 62.0619 56.5389C62.1752 56.2272 62.279 55.8872 62.3639 55.5283C62.8451 55.1033 63.3924 54.6688 64.0247 54.2437C63.7038 54.9238 63.3736 55.5283 63.0339 56.0667V56.0572ZM56.6644 55.5283C56.7494 55.8872 56.8532 56.2178 56.9664 56.5389C56.6739 56.3689 56.353 56.2178 56.0133 56.0667C55.6642 55.5377 55.3339 54.9238 55.0037 54.2437C55.6264 54.6688 56.1832 55.1033 56.655 55.5283H56.6644ZM64.2983 60.7893C64.0058 60.572 63.7133 60.3926 63.4302 60.2226C63.7699 60.1564 64.1285 60.0809 64.4965 59.9675C65.1193 60.1564 65.7798 60.4115 66.4686 60.7326C65.9402 60.7798 65.4212 60.8082 64.94 60.8082C64.7324 60.8082 64.5248 60.8082 64.2983 60.7893ZM62.0053 51.5046C61.4675 52.1847 60.9956 52.8553 60.5993 53.4881C60.4483 52.7514 60.2408 51.9675 59.9671 51.1457C60.2313 50.3901 60.5333 49.5967 60.9013 48.7561C61.1655 48.1421 61.4675 47.5093 61.7789 46.867C61.9581 48.5483 62.0336 50.1162 62.0053 51.5141V51.5046ZM56.1832 51.8069C56.2115 52.6664 56.287 53.4692 56.4002 54.2154C55.8057 53.7526 55.1358 53.2803 54.3997 52.827C53.8902 51.533 53.4184 50.0501 53.0032 48.4161C54.2204 49.5873 55.2962 50.7207 56.1832 51.8069ZM59.4575 66.2391C59.2877 65.5969 59.165 64.9924 59.0895 64.4257C59.2594 64.0951 59.4009 63.7645 59.5142 63.4434C59.6368 63.7645 59.7689 64.0856 59.9388 64.4162C59.8539 65.049 59.7123 65.7291 59.5142 66.4564C59.4953 66.3808 59.4764 66.3147 59.4575 66.2391ZM61.7789 72.1707C61.0334 70.6595 60.4295 69.2333 59.9671 67.9109C60.2408 67.0892 60.4483 66.3053 60.5993 65.5685C60.9956 66.2108 61.4675 66.872 62.0053 67.5615C62.0336 68.9499 61.9581 70.4989 61.7789 72.1707ZM35.5556 63.6984C38.8394 62.1494 42.01 60.8932 44.9352 59.977C46.6809 60.5154 48.3417 60.9309 49.8704 61.2143C48.5399 61.9982 47.1244 62.9428 45.6712 64.029C44.8503 64.0667 44.0105 64.0856 43.1329 64.0856C40.7455 64.0856 38.1883 63.9534 35.5556 63.6984ZM54.4752 69.2049C54.0129 69.6961 53.5127 70.1872 52.9937 70.6878C53.4089 69.0444 53.8807 67.552 54.3903 66.2486C55.1358 65.7858 55.7963 65.323 56.3908 64.8507C56.2775 65.5969 56.2021 66.3997 56.1643 67.2687C55.6642 67.8921 55.098 68.5438 54.4658 69.2049H54.4752ZM57.0324 67.5709C57.5703 66.8909 58.0421 66.2203 58.4384 65.5874C58.5894 66.3242 58.797 67.0987 59.0707 67.9204C58.6177 69.2333 58.0044 70.6689 57.2589 72.1802C57.0796 70.5084 57.0041 68.9594 57.0324 67.5709ZM64.1096 5.50655C64.1473 5.59156 64.1851 5.67656 64.2323 5.77101C64.2794 5.87491 64.3266 5.97881 64.3738 6.08271C65.4212 8.53846 66.2988 11.7404 66.9499 15.4334C64.8079 19.1643 62.7413 23.5752 60.9579 28.3828C60.4389 29.7901 59.9577 31.188 59.5236 32.5576C59.0895 31.188 58.6083 29.7996 58.0893 28.3828C56.3153 23.5752 54.2488 19.1643 52.0973 15.4334C53.6071 6.81943 56.4002 0.878404 59.5236 0.878404C59.6651 0.878404 59.7972 0.887849 59.9388 0.916184C61.4014 1.1712 62.8546 2.78633 64.119 5.50655H64.1096ZM51.0876 83.6655C53.324 80.6903 55.2301 77.7812 56.7399 75.061C57.2212 78.1212 57.9949 81.4931 59.0707 85.054C58.5422 86.8013 57.9478 88.5959 57.2872 90.3905C55.6831 94.7353 53.8241 98.7873 51.8803 102.282C51.2764 98.3528 50.9272 93.923 50.9272 89.2854C50.9272 87.368 50.9838 85.4884 51.0971 83.6655H51.0876ZM68.12 89.2854C68.12 93.923 67.7708 98.3528 67.1669 102.282C65.2325 98.7873 63.3641 94.7353 61.76 90.3905C61.09 88.5865 60.505 86.8013 59.9765 85.054C61.0428 81.5026 61.8166 78.1306 62.2978 75.061C63.8171 77.7812 65.7137 80.6903 67.9596 83.675C68.0634 85.5073 68.12 87.3775 68.12 89.2854ZM98.1743 97.6916C98.212 97.8239 98.2498 97.9561 98.2875 98.0789C98.3347 98.2583 98.3913 98.4472 98.4385 98.6267C98.4385 98.6456 98.4385 98.6645 98.448 98.6739C99.0613 101.224 98.9481 103.132 98.0611 104.171C97.9761 104.275 97.8723 104.369 97.7685 104.464C97.2684 104.879 96.589 105.087 95.7398 105.087C92.6352 105.087 87.8511 102.443 82.6234 98.0883C81.8402 93.8852 80.538 89.191 78.7263 84.2984C78.2073 82.891 77.66 81.5309 77.1033 80.1991C78.33 80.9642 79.6038 81.7387 80.9249 82.4849C85.3788 85.0162 89.8421 87.1036 93.9658 88.6243C95.8907 91.8923 97.3156 94.9242 98.1082 97.4744C98.1271 97.5405 98.1554 97.6161 98.1743 97.6916ZM102.581 44.6569C111.508 44.6569 117.603 46.4987 118.122 49.3606C118.5 51.4669 115.858 54.1115 110.875 56.5956C109.101 57.4834 107.101 58.3146 104.931 59.0891C100.845 57.6629 96.07 56.435 90.8801 55.5283C89.3986 55.2733 87.9171 55.0466 86.4545 54.8671C87.7662 54.1965 89.0684 53.4976 90.3894 52.7514C94.8339 50.2201 98.9103 47.4527 102.336 44.6758C102.421 44.6758 102.506 44.6758 102.59 44.6758L102.581 44.6569ZM106.176 59.5331C108.016 58.853 109.724 58.1163 111.262 57.3512C116.688 54.6404 119.425 51.7502 118.962 49.2095C118.377 45.9225 112.555 43.9201 103.345 43.8163C104.836 42.5506 106.195 41.2849 107.375 40.0382C111.527 35.665 113.102 32.0287 111.791 29.7901C110.97 28.3828 109.149 27.6744 106.374 27.6744C103.421 27.6744 99.552 28.4961 95.2585 29.979C99.5992 22.3662 100.977 16.1607 98.3158 13.96C97.6553 13.4216 96.7872 13.1383 95.7398 13.1383C92.6164 13.1383 87.9455 15.6129 82.8405 19.7405C83.1613 17.8136 83.3689 15.9907 83.4727 14.2717C83.803 8.29288 82.6329 4.5148 80.17 3.62695C79.8114 3.49472 79.4246 3.42861 79.0188 3.42861C75.8577 3.42861 71.6397 7.63172 67.6387 14.2622C67.0254 11.0792 66.2327 8.29288 65.2986 6.06381C65.2891 6.02603 65.2703 5.98825 65.2608 5.95992C65.1853 5.7899 65.1098 5.62934 65.0438 5.45932C64.9777 5.32709 64.9211 5.19486 64.8645 5.06262C64.8551 5.03429 64.8362 5.0154 64.8267 4.98706C63.2886 1.76625 61.4675 0.0377815 59.533 0.0377815C56.0511 0.0377815 53.0598 5.67656 51.4179 14.2717C47.4075 7.64116 43.1895 3.43805 40.0378 3.43805C39.6321 3.43805 39.2452 3.50417 38.8866 3.6364C35.6123 4.8076 34.763 11.1075 36.1973 19.731C31.0923 15.6129 26.4214 13.1477 23.3074 13.1477C22.26 13.1477 21.3825 13.4216 20.7314 13.9694C18.0609 16.1702 19.448 22.3757 23.7887 29.9885C19.4858 28.5056 15.6358 27.6839 12.6728 27.6839C9.90803 27.6839 8.0774 28.3923 7.25645 29.7996C5.52019 32.7748 8.96441 38.1302 15.683 43.8257C10.1345 43.8918 5.82215 44.638 3.14227 45.9509C1.3777 46.8104 0.320841 47.9155 0.0849362 49.2189C-0.518981 52.5908 4.57657 56.4634 12.8521 59.5519C4.53882 62.5839 -0.556726 66.4564 0.0471914 69.8378C0.641672 73.1247 6.44494 75.1176 15.6452 75.231C8.92666 80.9264 5.47301 86.2818 7.21871 89.2571C8.03966 90.6644 9.86085 91.3728 12.6351 91.3728C15.5981 91.3728 19.4669 90.5416 23.7698 89.0587C22.7979 90.7589 21.9675 92.4023 21.2881 93.9702C18.9007 99.4862 18.6931 103.425 20.6936 105.087C21.3541 105.635 22.2223 105.909 23.2697 105.909C26.3836 105.909 31.0546 103.444 36.1596 99.3257C34.7252 107.949 35.5745 114.249 38.8489 115.42C39.2169 115.552 39.5943 115.619 40.0001 115.619C43.1518 115.619 47.3698 111.416 51.3802 104.785C53.0221 113.371 56.0133 119.009 59.4953 119.009C62.9772 119.009 65.9685 113.371 67.6104 104.785C71.6114 111.416 75.8388 115.619 78.9905 115.619C79.3962 115.619 79.7831 115.552 80.1417 115.42C83.4161 114.249 84.2653 107.949 82.831 99.3257C87.9266 103.444 92.5975 105.909 95.7209 105.909C96.7777 105.909 97.6459 105.635 98.297 105.087C99.8728 103.784 100.071 101.064 98.9292 97.276C98.9009 97.1627 98.8631 97.0588 98.8348 96.9455C98.8065 96.8605 98.7688 96.766 98.7405 96.6716C97.995 94.4047 96.8155 91.8262 95.2396 89.0587C99.5331 90.5416 103.393 91.3728 106.355 91.3728C109.12 91.3728 110.951 90.6644 111.772 89.2571C113.508 86.2818 110.064 80.9264 103.345 75.231C112.546 75.1176 118.358 73.1247 118.943 69.8378C119.547 66.4564 114.442 62.5839 106.157 59.4953L106.176 59.5331Z",
p17200c00: "M9.23768 28.7625C8.74299 28.2677 8.74299 27.4656 9.23768 26.9711L26.0754 10.1333H15.2C14.5005 10.1333 13.9333 9.56622 13.9333 8.86667C13.9333 8.16711 14.5005 7.6 15.2 7.6H29.1333C29.4693 7.6 29.7915 7.73346 30.0291 7.97101C30.2665 8.20856 30.4 8.53072 30.4 8.86667V22.8C30.4 23.4996 29.8328 24.0667 29.1333 24.0667C28.4339 24.0667 27.8667 23.4996 27.8667 22.8V11.9247L11.029 28.7625C10.5343 29.257 9.73234 29.257 9.23768 28.7625Z",
p207700: "M0 10.7154H23.0072M23.0072 10.7154L15.6242 1M23.0072 10.7154L15.6242 20.4309",
p21c04900: "M32.2435 5.87951C31.8755 6.90581 31.681 7.92912 31.6661 8.95841C31.6511 9.9877 31.8127 11.017 32.1538 12.0523L32.2735 12.4083L31.9174 12.2886C30.9031 11.9505 29.8828 11.786 28.8565 11.7979C27.8272 11.8129 26.7919 12.0044 25.7446 12.3754L25.3706 12.5071L25.5053 12.1331C25.8733 11.1097 26.0678 10.0834 26.0828 9.05416C26.0977 8.02487 25.9361 6.99557 25.595 5.9603L25.4783 5.60424L25.8344 5.72392C26.8487 6.06203 27.8691 6.2266 28.8954 6.21463C29.9217 6.19967 30.9599 6.00817 32.0042 5.63715L32.3782 5.50549L32.2435 5.87951ZM37.6413 19.0868C36.6569 19.5505 35.7952 20.14 35.0561 20.8551C34.3171 21.5702 33.7037 22.414 33.213 23.3864L33.0454 23.7216L32.8779 23.3864C32.4021 22.429 31.7947 21.5942 31.0587 20.876C30.3196 20.1579 29.4519 19.5625 28.4495 19.0868L28.0905 18.9162L28.4495 18.7486C29.4339 18.2849 30.2957 17.6954 31.0347 16.9803C31.7738 16.2652 32.3872 15.4214 32.8779 14.446L33.0454 14.1108L33.213 14.446C33.6917 15.4034 34.2961 16.2412 35.0322 16.9564C35.7683 17.6745 36.636 18.2699 37.6413 18.7486L38.0004 18.9192L37.6413 19.0897V19.0868ZM31.7828 28.892C31.7977 29.9183 31.9892 30.9565 32.3602 32.0008L32.4919 32.3748L32.1179 32.2401C31.0916 31.8721 30.0683 31.6776 29.039 31.6627C28.0097 31.6477 26.9804 31.8093 25.9451 32.1504L25.5891 32.2701L25.7087 31.914C26.0469 30.8997 26.2114 29.8794 26.1994 28.8531C26.1845 27.8238 25.993 26.7885 25.622 25.7412L25.4903 25.3672L25.8643 25.5019C26.8876 25.8699 27.9139 26.0644 28.9432 26.0794C29.9725 26.0943 31.0018 25.9327 32.0371 25.5916L32.3932 25.4749L32.2735 25.831C31.9354 26.8453 31.7708 27.8657 31.7828 28.892ZM23.5544 33.2126C22.5969 33.6913 21.7591 34.2957 21.044 35.0318C20.3259 35.7679 19.7305 36.6356 19.2547 37.6409L19.0842 38L18.9136 37.6409C18.4498 36.6565 17.8604 35.7948 17.1453 35.0557C16.4302 34.3167 15.5864 33.7033 14.6139 33.2126L14.2788 33.045L14.6139 32.8775C15.5714 32.4017 16.4062 31.7943 17.1243 31.0583C17.8424 30.3222 18.4379 29.4515 18.9136 28.4491L19.0842 28.0901L19.2547 28.4491C19.7185 29.4335 20.305 30.2953 21.0231 31.0343C21.7382 31.7734 22.582 32.3868 23.5574 32.8775L23.8925 33.045L23.5574 33.2126H23.5544ZM12.4955 25.8609C12.1275 26.8842 11.933 27.9105 11.918 28.9398C11.9031 29.9691 12.0646 30.9984 12.4057 32.0337L12.5224 32.3897L12.1664 32.2701C11.152 31.932 10.1317 31.7674 9.10544 31.7794C8.07914 31.7943 7.04087 31.9858 5.99662 32.3568L5.6226 32.4885L5.75725 32.1145C6.12528 31.0882 6.31977 30.0649 6.33473 29.0356C6.34969 28.0063 6.18811 26.977 5.84701 25.9417L5.72732 25.5857L6.08339 25.7053C7.09772 26.0434 8.11803 26.208 9.14433 26.196C10.1736 26.1811 11.2089 25.9896 12.2561 25.6186L12.6302 25.4869L12.4955 25.8609ZM9.54827 19.2543C8.56386 19.7181 7.70213 20.3046 6.96307 21.0227C6.22402 21.7408 5.61063 22.5816 5.11992 23.557L4.95236 23.8921L4.78481 23.557C4.30607 22.5995 3.70166 21.7617 2.96559 21.0466C2.22953 20.3285 1.36181 19.7331 0.356459 19.2573L-0.00259579 19.0868L0.356459 18.9162C1.34087 18.4524 2.2026 17.863 2.94166 17.1479C3.68071 16.4297 4.2941 15.589 4.78481 14.6135L4.95236 14.2784L5.11992 14.6135C5.59567 15.571 6.20307 16.4058 6.93914 17.1239C7.6752 17.842 8.54591 18.4375 9.54827 18.9132L9.90732 19.0838L9.54827 19.2543ZM11.7983 9.14691C11.8133 10.1762 12.0048 11.2115 12.3758 12.2587L12.5075 12.6327L12.1335 12.4981C11.1102 12.1301 10.0839 11.9356 9.05457 11.9206C8.02528 11.9057 6.99599 12.0672 5.96071 12.4083L5.60465 12.525L5.72433 12.169C6.06244 11.1546 6.22701 10.1343 6.21504 9.10801C6.20008 8.08172 6.00858 7.04345 5.63756 5.9992L5.50591 5.62518L5.87992 5.75983C6.90622 6.12786 7.92953 6.32235 8.95882 6.33731C9.98811 6.35227 11.0174 6.19069 12.0527 5.84959L12.4087 5.7299L12.2891 6.08597C11.9509 7.1003 11.7864 8.12061 11.7983 9.14691ZM23.3839 5.1225C22.4264 5.59825 21.5916 6.20565 20.8735 6.94172C20.1554 7.68077 19.5599 8.54849 19.0842 9.55085L18.9136 9.9099L18.7431 9.55085C18.2793 8.56644 17.6928 7.70471 16.9747 6.96565C16.2566 6.2266 15.4158 5.61321 14.4404 5.1225L14.1053 4.95494L14.4404 4.78738C15.3979 4.30864 16.2357 3.70424 16.9508 2.96817C17.6689 2.23211 18.2643 1.36439 18.7401 0.359038L18.9106 -1.68744e-05L19.0812 0.359038C19.545 1.34345 20.1344 2.20518 20.8495 2.94424C21.5676 3.68329 22.4084 4.29668 23.3839 4.78738L23.719 4.95494L23.3839 5.1225Z",
p2744e400: "M100.707 8.70711C101.098 8.31658 101.098 7.68342 100.707 7.29289L94.3431 0.928932C93.9526 0.538408 93.3195 0.538408 92.9289 0.928932C92.5384 1.31946 92.5384 1.95262 92.9289 2.34315L98.5858 8L92.9289 13.6569C92.5384 14.0474 92.5384 14.6805 92.9289 15.0711C93.3195 15.4616 93.9526 15.4616 94.3431 15.0711L100.707 8.70711ZM0 8V9H100V8V7H0V8Z",
p2a66e780: "M53.4238 16.4142C54.2049 15.6332 54.2049 14.3668 53.4238 13.5858L40.6959 0.857864C39.9149 0.0768158 38.6485 0.0768158 37.8675 0.857864C37.0864 1.63891 37.0864 2.90524 37.8675 3.68629L49.1812 15L37.8675 26.3137C37.0864 27.0948 37.0864 28.3611 37.8675 29.1421C38.6485 29.9232 39.9149 29.9232 40.6959 29.1421L53.4238 16.4142ZM0 15V17H52.0096V15V13H0V15Z",
p2b170700: "M28.365 17.445H21.33C21.45 10.44 22.83 9.285 27.135 6.735C27.63 6.435 27.795 5.805 27.495 5.295C27.195 4.8 26.565 4.635 26.055 4.935C20.985 7.935 19.215 9.765 19.215 18.495V26.58C19.215 29.145 21.3 31.215 23.85 31.215H28.35C30.99 31.215 32.985 29.22 32.985 26.58V22.08C33 19.44 31.005 17.445 28.365 17.445Z",
p31536a40: "M16 102H0V134H16V102ZM236.335 129.314C242.584 123.065 242.584 112.935 236.335 106.686L134.512 4.86292C128.263 -1.38547 118.133 -1.38547 111.884 4.86292C105.636 11.1113 105.636 21.2419 111.884 27.4903L202.394 118L111.884 208.51C105.636 214.758 105.636 224.889 111.884 231.137C118.133 237.385 128.263 237.385 134.512 231.137L236.335 129.314ZM16 118V134H225.022V118V102H16V118Z",
p35158000: "M12.135 17.445H5.1C5.22 10.44 6.6 9.285 10.905 6.735C11.4 6.435 11.565 5.805 11.265 5.295C10.98 4.8 10.335 4.635 9.84 4.935C4.77 7.935 3 9.765 3 18.48V26.565C3 29.13 5.085 31.2 7.635 31.2H12.135C14.775 31.2 16.77 29.205 16.77 26.565V22.065C16.77 19.44 14.775 17.445 12.135 17.445Z",
p3a293080: "M811.779 405.153C541.186 405.153 270.593 405.153 0 405.153C0 181.393 181.723 0 405.889 0C630.056 0 811.779 181.393 811.779 405.153Z",
p6049d00: "M21.2252 15.76C20.8798 16.0947 20.7212 16.5787 20.7998 17.0533L21.9852 23.6133C22.0852 24.1693 21.8505 24.732 21.3852 25.0533C20.9292 25.3867 20.3225 25.4267 19.8252 25.16L13.9198 22.08C13.7145 21.9707 13.4865 21.912 13.2532 21.9053H12.8918C12.7665 21.924 12.6438 21.964 12.5318 22.0253L6.62517 25.12C6.33317 25.2667 6.0025 25.3187 5.6785 25.2667C4.88917 25.1173 4.3625 24.3653 4.49183 23.572L5.6785 17.012C5.75717 16.5333 5.5985 16.0467 5.25317 15.7067L0.438501 11.04C0.0358344 10.6493 -0.104166 10.0627 0.0798344 9.53333C0.258501 9.00533 0.714501 8.62 1.26517 8.53333L7.89183 7.572C8.39583 7.52 8.8385 7.21333 9.06517 6.76L11.9852 0.773331C12.0545 0.639997 12.1438 0.517331 12.2518 0.413331L12.3718 0.319997C12.4345 0.250664 12.5065 0.193331 12.5865 0.146664L12.7318 0.0933307L12.9585 -2.6226e-06H13.5198C14.0212 0.0519974 14.4625 0.351997 14.6932 0.799997L17.6518 6.76C17.8652 7.196 18.2798 7.49866 18.7585 7.572L25.3852 8.53333C25.9452 8.61333 26.4132 9 26.5985 9.53333C26.7732 10.068 26.6225 10.6547 26.2118 11.04L21.2252 15.76Z",
pd563480: "M6.32048 19.6796C5.98201 19.341 5.98201 18.7923 6.32048 18.4539L17.841 6.93332H10.4C9.92132 6.93332 9.5333 6.54529 9.5333 6.06665C9.5333 5.58801 9.92132 5.19998 10.4 5.19998H19.9333C20.1631 5.19998 20.3836 5.2913 20.5462 5.45383C20.7086 5.61637 20.8 5.83679 20.8 6.06665V15.6C20.8 16.0786 20.4119 16.4667 19.9333 16.4667C19.4547 16.4667 19.0666 16.0786 19.0666 15.6V8.15897L7.54612 19.6796C7.20767 20.0179 6.65893 20.0179 6.32048 19.6796Z",
ped78e80: "M0 10.7154H30.2757M30.2757 10.7154L20.5603 1M30.2757 10.7154L20.5603 20.4309",
}
