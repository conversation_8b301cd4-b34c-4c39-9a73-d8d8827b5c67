'use client'

import React, { useState, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import svgPaths from "../imports/svg-ef293kw09m";
import imgRoundCubeBlackMatte from "figma:asset/212e624bfa920a2f5c150ed2c48f40748de5ec5f.png";
import imgSphereIridescent from "figma:asset/71f8940d5885ea4b99defeb5890ef3cd386126b3.png";
import img from "figma:asset/6d7dce2f8945a9d3d48432859747dd0846373443.png";
import img1 from "figma:asset/b5e59d7a2371454573c4fb23ccaa2c5bd9eb7297.png";
import imgLilcodermanPinkChairSittingOnTableInARoomInTheStyle8F5E5Aa938F84Af089F38572B0Ae93621 from "figma:asset/bdf05f127e983c7a87434379e33db88c745be221.png";
import imgImage from "figma:asset/ab325d5d46b804b1ce04547ea0b4bd6703d39370.png";
import imgRectangle31 from "figma:asset/d18f3ca1c09872c27e4fb88ba1980b61a0d8dbd5.png";
import imgRectangle28 from "figma:asset/b4af99a3c7f40f4c045a5748c8d707912e22f2c1.png";
import imgRectangle26 from "figma:asset/a9014fc1fa453c03c2c52f91fff1f2754e20ad58.png";

function AnimatedCube({ children, delay = 0, ...props }) {
  return (
    <motion.div
      {...props}
      initial={{ rotateX: 0, rotateY: 0 }}
      animate={{ 
        rotateX: [0, 10, -10, 0],
        rotateY: [0, 15, -15, 0] 
      }}
      transition={{
        duration: 6,
        repeat: Infinity,
        delay,
        ease: "easeInOut"
      }}
    >
      {children}
    </motion.div>
  );
}

function AnimatedSphere({ children, delay = 0, ...props }) {
  return (
    <motion.div
      {...props}
      initial={{ scale: 1 }}
      animate={{ scale: [1, 1.1, 0.9, 1] }}
      transition={{
        duration: 4,
        repeat: Infinity,
        delay,
        ease: "easeInOut"
      }}
    >
      {children}
    </motion.div>
  );
}

function Cube2() {
  return (
    <div className="relative size-full" data-name="Cube-2">
      <div
        className="absolute bg-center bg-cover bg-no-repeat blur-[2px] filter inset-0"
        data-name="RoundCube-Black-Matte"
        style={{ backgroundImage: `url('${imgRoundCubeBlackMatte}')` }}
      />
    </div>
  );
}

function Cube3() {
  return (
    <div className="relative size-full" data-name="Cube-2">
      <div
        className="absolute bg-center bg-cover bg-no-repeat inset-0"
        data-name="RoundCube-Black-Matte"
        style={{ backgroundImage: `url('${imgRoundCubeBlackMatte}')` }}
      />
    </div>
  );
}

function Shapes() {
  const { scrollYProgress } = useScroll();
  const rotateY = useTransform(scrollYProgress, [0, 1], [0, 360]);
  
  return (
    <div
      className="absolute h-[719px] top-6 w-[673px]"
      data-name="Shapes"
      style={{ left: "calc(25% + 45px)" }}
    >
      <motion.div 
        className="absolute bottom-[1.91%] flex items-center justify-center left-[90.359%] right-[-7.412%] top-[82.56%]"
        style={{ rotateY }}
      >
        <AnimatedSphere delay={0} className="flex-none h-[83.202px] rotate-[332.946deg] skew-x-[1.293deg] w-[84.508px]">
          <div
            className="bg-center bg-cover bg-no-repeat size-full"
            data-name="Sphere-Iridescent"
            style={{ backgroundImage: `url('${imgSphereIridescent}')` }}
          />
        </AnimatedSphere>
      </motion.div>
      
      <motion.div 
        className="absolute bottom-[81.209%] flex items-center justify-center left-[-74.889%] right-[124.732%] top-[-26.886%]"
        initial={{ opacity: 0.7 }}
        animate={{ opacity: [0.7, 1, 0.7] }}
        transition={{ duration: 3, repeat: Infinity }}
      >
        <AnimatedSphere delay={1} className="flex-none h-[244.711px] rotate-[332.946deg] skew-x-[1.293deg] w-[248.554px]">
          <div
            className="bg-center bg-cover bg-no-repeat blur-[2.4px] filter size-full"
            data-name="Sphere-Iridescent"
            style={{ backgroundImage: `url('${imgSphereIridescent}')` }}
          />
        </AnimatedSphere>
      </motion.div>
      
      <div className="absolute flex h-[389.957px] items-center justify-center left-[-74.889%] right-[118.057%] top-[482px]">
        <AnimatedCube delay={0.5} className="flex-none h-[313.432px] rotate-[341.68deg] skew-x-[0.465deg] w-[296.572px]">
          <Cube2 />
        </AnimatedCube>
      </div>
      
      <div className="absolute flex h-[63.31px] items-center justify-center left-[13.133%] right-[77.332%] top-[261.527px]">
        <AnimatedCube delay={1} className="flex-none h-[50.069px] rotate-[341.68deg] skew-x-[0.465deg] w-[50.609px]">
          <Cube3 />
        </AnimatedCube>
      </div>
      
      <div className="absolute flex h-[70.701px] items-center justify-center left-[96.021%] right-[-6.669%] top-[411.527px]">
        <AnimatedCube delay={1.5} className="flex-none h-[50.351px] rotate-[45.541deg] skew-x-[359.229deg] w-[50.329px]">
          <Cube3 />
        </AnimatedCube>
      </div>
      
      <div className="absolute flex h-[171.353px] items-center justify-center left-[133.923%] right-[-61.746%] top-[-23.409px]">
        <AnimatedCube delay={2} className="flex-none h-[166.742px] rotate-[261.528deg] skew-x-[359.453deg] w-[150px]">
          <Cube2 />
        </AnimatedCube>
      </div>
      
      <div className="absolute flex h-[105.821px] items-center justify-center left-[-15.453%] right-[99.942%] top-[482px]">
        <AnimatedCube delay={2.5} className="flex-none h-[77.798px] rotate-[151.763deg] skew-x-[359.229deg] w-[77.764px]">
          <Cube3 />
        </AnimatedCube>
      </div>
    </div>
  );
}

function NavigationLinks() {
  const [activeLink, setActiveLink] = useState('');
  
  const scrollToSection = (sectionId: string) => {
    setActiveLink(sectionId);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div
      className="box-border content-stretch flex flex-row font-['Montserrat:Bold',_sans-serif] font-bold gap-8 h-[50px] items-center justify-start p-0 relative shrink-0 text-[#ffffff] text-[18px] text-left text-nowrap w-auto"
      data-name="Links"
    >
      {['About', 'Services', 'Our Work', 'Blog'].map((link, index) => {
        const sectionId = link.toLowerCase().replace(' ', '-');
        return (
          <motion.div 
            key={link}
            className="relative shrink-0 cursor-pointer"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => scrollToSection(sectionId)}
          >
            <p className={`block leading-[1.3] text-nowrap whitespace-pre transition-colors duration-300 ${
              activeLink === sectionId ? 'text-[#feb273]' : 'hover:text-[#feb273]'
            }`}>
              {link}
            </p>
          </motion.div>
        );
      })}
    </div>
  );
}

function LetsTalk() {
  return (
    <motion.div
      className="grid-cols-[max-content] grid-rows-[max-content] inline-grid place-items-start relative shrink-0 cursor-pointer z-10"
      data-name="Lets Talk"
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={() => {
        const contactSection = document.getElementById('contact');
        if (contactSection) {
          contactSection.scrollIntoView({ behavior: 'smooth' });
        }
      }}
    >
      <div
        className="[background-size:126.44%_111.86%] [grid-area:1_/_1] bg-[50.72%_100%] bg-no-repeat h-[24px] mask-alpha mask-intersect mask-no-clip mask-no-repeat mask-position-[0px_0px] mask-size-[128px_24px] w-[128px]"
        data-name="画形态"
        style={{
          backgroundImage: `url('${img}')`,
          maskImage: `url('${img1}')`,
        }}
      />
      <div
        className="[grid-area:1_/_1] font-['Montserrat:Bold',_sans-serif] font-bold leading-[0] mask-alpha mask-intersect mask-no-clip mask-no-repeat mask-position-[0px_0px] mask-size-[128px_24px] relative text-[#ffffff] text-[16px] text-left text-nowrap"
        style={{ maskImage: `url('${img1}')` }}
      >
        <p className="block leading-[1.5] whitespace-pre text-center pt-1" dir="auto">
          Let's Talk
        </p>
      </div>
    </motion.div>
  );
}

function Navbar() {
  return (
    <div
      className="absolute box-border content-stretch flex flex-row h-[51.123px] items-center justify-between leading-[0] left-0 px-[124px] py-0 top-[31.035px] w-[1437px]"
      data-name="Navbar"
    >
      <NavigationLinks />
      <LetsTalk />
    </div>
  );
}

function Navbar1() {
  return (
    <div className="absolute contents left-0 top-[31.035px]" data-name="Navbar">
      <motion.div
        className="absolute bg-[#131d26] h-[44.988px] rounded-[25px] top-[31.104px] w-[836px]"
        style={{ left: "calc(25% - 21px)" }}
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      />
      <Navbar />
    </div>
  );
}

function Middle() {
  return (
    <div
      className="absolute contents top-[31px]"
      data-name="Middle"
      style={{ left: "calc(8.333% + 28px)" }}
    >
      <motion.div
        className="absolute h-[405.153px] top-[337.282px] w-[811.779px]"
        style={{ left: "calc(25% - 23px)" }}
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.4 }}
      >
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 812 406"
        >
          <path
            d={svgPaths.p3a293080}
            fill="var(--fill-0, #FEB273)"
            id="Ellipse 2"
          />
        </svg>
      </motion.div>
      
      <motion.div
        className="absolute flex h-[405.153px] items-center justify-center top-[737.282px] w-[811.779px]"
        style={{ left: "calc(25% - 23px)" }}
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.6 }}
      >
        <div className="flex-none rotate-[180deg]">
          <div className="h-[405.153px] relative w-[811.779px]">
            <svg
              className="block size-full"
              fill="none"
              preserveAspectRatio="none"
              viewBox="0 0 812 406"
            >
              <path
                d={svgPaths.p3a293080}
                fill="var(--fill-0, #FEB273)"
                id="Ellipse 3"
              />
            </svg>
          </div>
        </div>
      </motion.div>
      
      <motion.div
        className="absolute font-['Playfair_Display:Bold',_sans-serif] font-bold leading-[0] text-[#ffffff] text-[250px] text-left text-nowrap top-[337px]"
        style={{ left: "calc(16.667% + 68px)" }}
        initial={{ x: -100, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 1, delay: 0.8 }}
      >
        <p className="block leading-[normal] whitespace-pre">Design</p>
      </motion.div>
      
      <motion.div
        className="absolute font-['Playfair_Display:Bold',_sans-serif] font-bold leading-[0] text-[#131d26] text-[309.341px] text-left text-nowrap top-[31px]"
        style={{ left: "calc(8.333% + 28px)" }}
        initial={{ x: 100, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 1, delay: 1 }}
      >
        <p className="block leading-[normal] whitespace-pre">Creative</p>
      </motion.div>
    </div>
  );
}

function IconOutlinedDirectionsStraightArrowsUpRight() {
  return (
    <div
      className="relative shrink-0 size-[42px]"
      data-name="icon / outlined / directions / straight arrows / up right"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 42 42"
      >
        <g id="icon / outlined / directions / straight arrows / up right">
          <path
            d="M12.25 29.75L29.75 12.25"
            id="Vector"
            stroke="var(--stroke-0, white)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          />
          <path
            d="M12.25 12.25H29.75V29.75"
            id="Vector_2"
            stroke="var(--stroke-0, white)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          />
        </g>
      </svg>
    </div>
  );
}

function Portfolio() {
  return (
    <motion.div
      className="bg-[#131d26] relative rounded-[60px] shrink-0 w-52 cursor-pointer"
      data-name="Portfolio"
      whileHover={{ scale: 1.05, backgroundColor: "#1a2530" }}
      whileTap={{ scale: 0.95 }}
      onClick={() => {
        const portfolioSection = document.getElementById('our-work');
        if (portfolioSection) {
          portfolioSection.scrollIntoView({ behavior: 'smooth' });
        }
      }}
    >
      <div className="box-border content-stretch flex flex-row items-center justify-center overflow-clip px-5 py-2.5 relative w-52">
        <div className="font-['Lufga:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#ffffff] text-[25.692px] text-left text-nowrap tracking-[-0.3854px]">
          <p className="adjustLetterSpacing block leading-[normal] whitespace-pre">
            Portfolio
          </p>
        </div>
        <motion.div
          animate={{ x: [0, 3, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <IconOutlinedDirectionsStraightArrowsUpRight />
        </motion.div>
      </div>
      <div className="absolute border-[#d0d5dd] border-[0.5px] border-solid inset-0 pointer-events-none rounded-[60px]" />
    </motion.div>
  );
}

function HireMe() {
  return (
    <motion.div
      className="basis-0 grow min-h-px min-w-px relative rounded-[60px] shrink-0 cursor-pointer"
      data-name="Hire me"
      whileHover={{ backgroundColor: "#feb273" }}
      whileTap={{ scale: 0.95 }}
      onClick={() => {
        const contactSection = document.getElementById('contact');
        if (contactSection) {
          contactSection.scrollIntoView({ behavior: 'smooth' });
        }
      }}
    >
      <div className="flex flex-row items-center justify-center overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-center px-5 py-2.5 relative w-full">
          <div className="font-['Lufga:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#131d26] text-[25.692px] text-left text-nowrap tracking-[-0.3854px]">
            <p className="adjustLetterSpacing block leading-[normal] whitespace-pre">
              Hire me
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

function Button5() {
  return (
    <motion.div
      className="absolute backdrop-blur-[7.5px] backdrop-filter bg-[rgba(255,255,255,0.1)] h-[83.841px] rounded-[50px] top-[617.43px] translate-x-[-50%] w-[348px]"
      data-name="Button"
      style={{ left: "calc(54.167% - 44px)" }}
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, delay: 1.2 }}
      whileHover={{ y: -5 }}
    >
      <div className="box-border content-stretch flex flex-row gap-2.5 h-[83.841px] items-center justify-center overflow-clip p-[10px] relative w-[348px]">
        <Portfolio />
        <HireMe />
      </div>
      <div className="absolute border-2 border-[#ffffff] border-solid inset-0 pointer-events-none rounded-[50px]" />
    </motion.div>
  );
}

function VuesaxBoldQuoteUp() {
  return (
    <div className="absolute contents inset-0" data-name="vuesax/bold/quote-up">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 36 36"
      >
        <g id="quote-up">
          <g id="Vector" opacity="0"></g>
          <path
            d={svgPaths.p35158000}
            fill="var(--fill-0, #344054)"
            id="Vector_2"
          />
          <path
            d={svgPaths.p2b170700}
            fill="var(--fill-0, #344054)"
            id="Vector_3"
          />
        </g>
      </svg>
    </div>
  );
}

function VuesaxBoldQuoteUp1() {
  return (
    <div className="relative shrink-0 size-9" data-name="vuesax/bold/quote-up">
      <VuesaxBoldQuoteUp />
    </div>
  );
}

function Frame4() {
  return (
    <motion.div 
      className="absolute box-border content-stretch flex flex-col gap-6 h-[138.734px] items-start justify-start left-[49px] p-0 top-[371px]"
      initial={{ x: -50, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: 0.8, delay: 1.4 }}
    >
      <VuesaxBoldQuoteUp1 />
      <div className="font-['Lufga:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#344054] text-[20px] text-left text-nowrap tracking-[-0.3px]">
        <p className="adjustLetterSpacing block leading-[normal] whitespace-pre">
          Jenny's Exceptional product design
          <br />
          ensure our website's success.
          <br />
          Highly Recommended
        </p>
      </div>
    </motion.div>
  );
}

function Star() {
  return (
    <motion.div
      className="absolute bottom-[10.411%] left-[8.331%] right-[8.324%] top-[10.417%]"
      data-name="Star"
      animate={{ rotate: [0, 360] }}
      transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 27 26"
      >
        <g id="Star">
          <path
            d={svgPaths.p6049d00}
            fill="var(--fill-0, #FD853A)"
            id="Star_2"
          />
        </g>
      </svg>
    </motion.div>
  );
}

function IconStar() {
  return (
    <div className="relative shrink-0 size-8" data-name="icon/Star">
      <Star />
    </div>
  );
}

function Frame5() {
  return (
    <div className="box-border content-stretch flex flex-row items-start justify-center p-0 relative shrink-0">
      {[...Array(5).keys()].map((_, i) => (
        <motion.div
          key={i}
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ 
            duration: 0.5, 
            delay: 1.6 + (i * 0.1),
            type: "spring",
            stiffness: 260,
            damping: 20
          }}
        >
          <IconStar />
        </motion.div>
      ))}
    </div>
  );
}

function Frame3() {
  return (
    <motion.div 
      className="box-border content-stretch flex flex-col gap-[5px] items-end justify-start leading-[0] p-0 relative shrink-0 text-center text-neutral-900 text-nowrap"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.6, delay: 2.1, type: "spring" }}
    >
      <div className="font-['Urbanist:Bold',_sans-serif] font-bold relative shrink-0 text-[47px] tracking-[-0.705px]">
        <motion.p 
          className="adjustLetterSpacing block leading-none text-nowrap whitespace-pre"
          animate={{ color: ["#000000", "#feb273", "#000000"] }}
          transition={{ duration: 2, repeat: Infinity, delay: 3 }}
        >
          10 Years
        </motion.p>
      </div>
      <div className="font-['Lufga:Regular',_sans-serif] not-italic relative shrink-0 text-[20px] tracking-[-0.3px]">
        <p className="adjustLetterSpacing block leading-none text-nowrap whitespace-pre">
          Experience
        </p>
      </div>
    </motion.div>
  );
}

function Frame6() {
  return (
    <div
      className="absolute box-border content-stretch flex flex-col gap-[21px] h-[125.665px] items-end justify-start p-0 top-[605px]"
      style={{ left: "calc(83.333% + 51px)" }}
    >
      <Frame5 />
      <Frame3 />
    </div>
  );
}

function HeroSection() {
  return (
    <section id="hero" className="absolute contents left-0 top-6" data-name="Hero Section">
      <Shapes />
      <Navbar1 />
      <Middle />
      <Button5 />
      <Frame4 />
      <Frame6 />
    </section>
  );
}

// Social Media Icons Components
function InstagramIcon() {
  return (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
    </svg>
  );
}

function LinkedInIcon() {
  return (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
    </svg>
  );
}

function TwitterIcon() {
  return (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
    </svg>
  );
}

function SocialMediaButton({ icon, platform, delay = 0 }) {
  const handleClick = () => {
    // Handle social media navigation
    const urls = {
      instagram: 'https://instagram.com',
      linkedin: 'https://linkedin.com',
      twitter: 'https://twitter.com'
    };
    window.open(urls[platform], '_blank');
  };

  return (
    <motion.button
      className="bg-[rgba(255,255,255,0.2)] backdrop-blur-sm rounded-full p-3 hover:bg-[rgba(255,255,255,0.3)] transition-all duration-300 cursor-pointer group"
      initial={{ scale: 0, opacity: 0 }}
      whileInView={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.5, delay, type: "spring", stiffness: 200 }}
      whileHover={{ scale: 1.1, y: -2 }}
      whileTap={{ scale: 0.95 }}
      onClick={handleClick}
      viewport={{ once: true }}
    >
      <div className="text-[#000000] group-hover:text-[#feb273] transition-colors duration-300">
        {icon}
      </div>
    </motion.button>
  );
}

export default function InteractiveDigitalAgency() {
  const [currentSection, setCurrentSection] = useState('hero');

  useEffect(() => {
    const handleScroll = () => {
      const sections = ['hero', 'about', 'our-work', 'contact', 'blog'];
      const scrollPosition = window.scrollY + window.innerHeight / 2;

      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setCurrentSection(section);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div
      className="bg-[#ffffff] relative size-full"
      data-name="Digital Agency Website"
    >
      <HeroSection />
      
      {/* About Section */}
      <section id="about" className="absolute contents left-[22px] top-[760px]" data-name="Section 2">
        {/* Slogan/Intro */}
        <motion.div
          className="absolute bg-[#fadcd9] h-[479px] left-[22px] overflow-hidden rounded-[20px] top-[760px] w-[565px]"
          data-name="SLOGAN/INTRO"
          initial={{ x: -50, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="absolute font-['Gilroy:Bold',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[56px] text-left w-[475px]"
            style={{ top: "calc(50% - 50px)" }}
            whileHover={{ scale: 1.02 }}
          >
            <p className="leading-[60px]">
              <span>Artist Redefining </span>
              <motion.span 
                className="font-['Gilroy:Light_Italic',_sans-serif] italic"
                animate={{ color: ["#000000", "#feb273", "#000000"] }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                Architecture
              </motion.span>
              <span> with AI-Driven Design</span>
            </p>
          </motion.div>
          
          <motion.div
            className="absolute right-6 size-[119px] top-[34px]"
            data-name="FLOWER ICON"
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
          >
            <svg
              className="block size-full"
              fill="none"
              preserveAspectRatio="none"
              viewBox="0 0 119 119"
            >
              <g clipPath="url(#clip0_1_366)" id="FLOWER ICON">
                <path
                  d={svgPaths.p12a08980}
                  fill="var(--fill-0, #F8AFA6)"
                  id="Vector"
                />
              </g>
              <defs>
                <clipPath id="clip0_1_366">
                  <rect fill="white" height="119" width="119" />
                </clipPath>
              </defs>
            </svg>
          </motion.div>
        </motion.div>

        {/* Portrait */}
        <motion.div
          className="absolute h-[476px] overflow-hidden rounded-[20px] top-[763px] w-[330px]"
          data-name="PORTRAIT"
          style={{ left: "calc(41.667% + 12px)" }}
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <div
            className="absolute bg-center bg-cover bg-no-repeat h-[556px] left-[-25px] top-[-19px] w-[355px]"
            data-name="IMAGE"
            style={{ backgroundImage: `url('${imgImage}')` }}
          />
        </motion.div>

        {/* Work Section */}
        <motion.div
          id="our-work"
          className="absolute bg-[#fadcd9] h-[726px] overflow-hidden rounded-[20px] top-[763px] w-[447px]"
          data-name="WORK"
          style={{ left: "calc(66.667% + 6px)" }}
          initial={{ x: 50, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          {/* Work Title */}
          <motion.div
            className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-6"
            initial={{ y: 10, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <p className="block leading-[normal] whitespace-pre">Musea</p>
          </motion.div>

          {/* Main Project Image */}
          <motion.div
            className="absolute h-[269px] left-6 overflow-hidden rounded-[12px] top-[60px] w-[399px]"
            initial={{ scale: 0.95, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            whileHover={{ scale: 1.02 }}
            viewport={{ once: true }}
          >
            <div
              className="h-[400px] w-[399px] bg-center bg-cover bg-no-repeat"
              style={{
                backgroundImage: `url('${imgLilcodermanPinkChairSittingOnTableInARoomInTheStyle8F5E5Aa938F84Af089F38572B0Ae93621}')`,
                backgroundPosition: 'center -113px'
              }}
            />
          </motion.div>

          {/* Other Projects */}
          <motion.div
            className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-[360px]"
            initial={{ x: -10, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            whileHover={{ color: "#feb273" }}
            viewport={{ once: true }}
          >
            <p className="block leading-[normal] whitespace-pre cursor-pointer">Elara</p>
          </motion.div>

          <motion.div
            className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-[420px]"
            initial={{ x: -10, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            whileHover={{ color: "#feb273" }}
            viewport={{ once: true }}
          >
            <p className="block leading-[normal] whitespace-pre cursor-pointer">Verve</p>
          </motion.div>

          <motion.div
            className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-[480px]"
            initial={{ x: -10, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 1.0 }}
            whileHover={{ color: "#feb273" }}
            viewport={{ once: true }}
          >
            <p className="block leading-[normal] whitespace-pre cursor-pointer">Zephyr</p>
          </motion.div>

          {/* Updated Social Frame - With Instagram, LinkedIn, Twitter */}
          <motion.div
            className="absolute bottom-6 left-6 right-6"
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 1.2 }}
            viewport={{ once: true }}
          >
            <div className="bg-[rgba(255,255,255,0.2)] backdrop-blur-sm rounded-[16px] p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <p className="text-[#000000] font-['Lufga:Medium',_sans-serif] text-[14px] mb-1">Social Projects</p>
                  <p className="text-[#666666] font-['Lufga:Light',_sans-serif] text-[11px]">Follow our journey</p>
                </div>
              </div>
              
              {/* Social Media Icons Row */}
              <div className="flex items-center gap-3">
                <SocialMediaButton 
                  icon={<InstagramIcon />} 
                  platform="instagram" 
                  delay={0.1}
                />
                <SocialMediaButton 
                  icon={<LinkedInIcon />} 
                  platform="linkedin" 
                  delay={0.2}
                />
                <SocialMediaButton 
                  icon={<TwitterIcon />} 
                  platform="twitter" 
                  delay={0.3}
                />
                
                <motion.div
                  className="ml-auto text-[#feb273] cursor-pointer font-['Lufga:Medium',_sans-serif] text-[12px]"
                  whileHover={{ x: 3 }}
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  Connect →
                </motion.div>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* About */}
        <motion.div
          className="absolute bg-[#fadcd9] h-[351px] left-[22px] overflow-hidden rounded-[20px] top-[1263px] w-[448px]"
          data-name="ABOUT"
          initial={{ scale: 0.9, opacity: 0 }}
          whileInView={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <div
            className="absolute font-['Gilroy:Light',_sans-serif] leading-[25px] left-6 not-italic text-[#000000] text-[20px] text-left w-[296px]"
            style={{ top: "calc(50% - 20.5px)" }}
          >
            <p>
              Julia Huang is an innovative AI artist, renowned for blending
              cutting-edge technology with creative expression. Based in LA, she
              crafts unique digital art experiences accessible globally.
            </p>
          </div>
          
          <div className="absolute left-6 size-[38px] top-8" data-name="CIRCLE ICON">
            <svg
              className="block size-full"
              fill="none"
              preserveAspectRatio="none"
              viewBox="0 0 38 38"
            >
              <g clipPath="url(#clip0_1_396)" id="CIRCLE ICON">
                <motion.path
                  d={svgPaths.p21c04900}
                  fill="var(--fill-0, #F8AFA6)"
                  id="Vector"
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                />
              </g>
              <defs>
                <clipPath id="clip0_1_396">
                  <rect fill="white" height="38" width="38" />
                </clipPath>
              </defs>
            </svg>
          </div>
        </motion.div>

        {/* Contact Section - Fixed with rounded corners */}
        <section id="contact" className="absolute contents">
          <motion.div
            className="absolute bg-[#f8afa6] h-[351px] overflow-hidden rounded-[20px] top-[1263px] w-[448px]"
            data-name="CONTACT"
            style={{ left: "calc(33.333% + 14px)" }}
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6 }}
            whileHover={{ scale: 1.02, backgroundColor: "#f6a194" }}
            viewport={{ once: true }}
          >
            <motion.div 
              className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[55px] text-left text-nowrap top-[259px]"
              whileHover={{ color: "#ffffff" }}
              transition={{ duration: 0.3 }}
            >
              <p className="block leading-[normal] whitespace-pre">Contact me</p>
            </motion.div>
            
            <div className="absolute box-border content-stretch flex flex-row items-end justify-between left-1/2 p-0 top-[30px] translate-x-[-50%] w-[400px]">
              <div className="font-['Gilroy:Light',_sans-serif] leading-[normal] not-italic relative shrink-0 text-[#000000] text-[15px] text-left text-nowrap whitespace-pre">
                <p className="block mb-0">Have some</p>
                <p className="block">questions?</p>
              </div>
              <motion.div
                className="relative shrink-0 size-[38px] cursor-pointer"
                whileHover={{ scale: 1.2, rotate: 45 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg
                  className="block size-full"
                  fill="none"
                  preserveAspectRatio="none"
                  viewBox="0 0 38 38"
                >
                  <g id="ARROW">
                    <path
                      clipRule="evenodd"
                      d={svgPaths.p17200c00}
                      fill="var(--fill-0, black)"
                      fillRule="evenodd"
                      id="Vector"
                    />
                  </g>
                </svg>
              </motion.div>
            </div>
          </motion.div>
        </section>

        <motion.div 
          className="absolute font-['Inter:Black',_sans-serif] font-black h-[77px] leading-[77px] left-[46px] not-italic text-[#131d26] text-[78px] text-left top-[1316px] tracking-[1.56px] w-[430px]"
          initial={{ x: -100, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 1 }}
          viewport={{ once: true }}
        >
          <p className="adjustLetterSpacing">PER PIXEL</p>
        </motion.div>
      </section>

      {/* Portfolio Section - Fixed with proper lines, 2025, and repositioned blog */}
      <motion.section 
        className="absolute contents left-[-40px] top-[1658px]"
        data-name="Section 3"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 1 }}
        viewport={{ once: true }}
      >
        {/* Background */}
        <motion.div
          className="absolute bg-[#feb273] h-[830px] rounded-[68px] top-[1658px] w-[913px]"
          data-name="bg"
          style={{ left: "calc(33.333% + 20px)" }}
          whileHover={{ backgroundColor: "#fd9f4f" }}
          transition={{ duration: 0.3 }}
        />

        {/* Year 2025 */}
        <motion.div
          className="absolute font-['Playfair_Display:Bold',_sans-serif] font-bold leading-[0] text-[#ffffff] text-[180px] text-left text-nowrap top-[1780px]"
          style={{ left: "calc(41.667% + 40px)" }}
          initial={{ scale: 0 }}
          whileInView={{ scale: 1 }}
          transition={{ duration: 1, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <p className="block leading-[normal] whitespace-pre opacity-20">2025</p>
        </motion.div>

        {/* Decorative Lines - Multiple animated lines */}
        <motion.div
          className="absolute top-[1720px] h-[1px] bg-[rgba(0,0,0,0.2)]"
          style={{ left: "calc(33.333% + 60px)", width: "calc(55% - 120px)" }}
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1.5, delay: 0.3 }}
          viewport={{ once: true }}
        />
        
        <motion.div
          className="absolute top-[1950px] h-[1px] bg-[rgba(0,0,0,0.15)]"
          style={{ left: "calc(45% + 40px)", width: "calc(35% - 80px)" }}
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1.2, delay: 0.5 }}
          viewport={{ once: true }}
        />

        <motion.div
          className="absolute top-[2180px] h-[1px] bg-[rgba(0,0,0,0.2)]"
          style={{ left: "calc(38% + 20px)", width: "calc(50% - 100px)" }}
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1.8, delay: 0.7 }}
          viewport={{ once: true }}
        />

        <motion.div
          className="absolute top-[2350px] h-[1px] bg-[rgba(0,0,0,0.1)]"
          style={{ left: "calc(42% + 60px)", width: "calc(40% - 120px)" }}
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1.3, delay: 0.9 }}
          viewport={{ once: true }}
        />
        
        <motion.div
          className="absolute font-['Glegoo:Bold',_sans-serif] leading-[120px] not-italic text-[#ffffff] text-[120px] text-left text-nowrap top-[2073px]"
          style={{ left: "calc(50% + 22px)" }}
          animate={{ x: [0, 10, 0] }}
          transition={{ duration: 4, repeat: Infinity }}
        >
          <p className="whitespace-pre">PORTFOLIO</p>
        </motion.div>

        <motion.div
          className="absolute flex h-[168.995px] items-center justify-center top-[1989px] w-[122.997px]"
          style={{ left: "calc(16.667% + 40px)" }}
          animate={{ rotate: [0, 10, -10, 0] }}
          transition={{ duration: 3, repeat: Infinity }}
        >
          <div className="flex-none rotate-[53.952deg]">
            <div className="h-0 relative w-[209.022px]">
              <div className="absolute bottom-[-117.823px] left-[-7.655%] right-[-7.655%] top-[-117.823px]">
                <svg
                  className="block size-full"
                  fill="none"
                  preserveAspectRatio="none"
                  viewBox="0 0 242 236"
                >
                  <path
                    d={svgPaths.p31536a40}
                    fill="var(--stroke-0, black)"
                    id="Arrow 1"
                  />
                </svg>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.section>

      {/* Latest Insights - Repositioned outside orange section */}
      <motion.section
        id="blog"
        className="absolute left-[22px] top-[2550px]"
        initial={{ y: 50, opacity: 0 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <motion.div
          className="bg-[#131d26] rounded-[20px] p-8 w-[500px]"
          whileHover={{ scale: 1.02 }}
        >
          <h3 className="font-['Gilroy:Bold',_sans-serif] text-[36px] text-[#ffffff] mb-6">
            Latest Insights
          </h3>
          <div className="space-y-6">
            <motion.div
              className="bg-[rgba(255,255,255,0.1)] rounded-[12px] p-6 cursor-pointer"
              whileHover={{ backgroundColor: "rgba(255,255,255,0.15)" }}
            >
              <h4 className="font-['Lufga:Medium',_sans-serif] text-[20px] text-[#ffffff] mb-3">
                AI in Creative Design
              </h4>
              <p className="font-['Lufga:Light',_sans-serif] text-[16px] text-[rgba(255,255,255,0.8)] leading-[24px]">
                Exploring how artificial intelligence is reshaping the creative landscape and opening new possibilities...
              </p>
              <motion.div
                className="mt-4 text-[#feb273] font-['Lufga:Medium',_sans-serif] text-[14px]"
                whileHover={{ x: 5 }}
              >
                Read more →
              </motion.div>
            </motion.div>
            <motion.div
              className="bg-[rgba(255,255,255,0.1)] rounded-[12px] p-6 cursor-pointer"
              whileHover={{ backgroundColor: "rgba(255,255,255,0.15)" }}
            >
              <h4 className="font-['Lufga:Medium',_sans-serif] text-[20px] text-[#ffffff] mb-3">
                Future of Digital Art
              </h4>
              <p className="font-['Lufga:Light',_sans-serif] text-[16px] text-[rgba(255,255,255,0.8)] leading-[24px]">
                A deep dive into emerging trends and technologies that are defining the future of digital art...
              </p>
              <motion.div
                className="mt-4 text-[#feb273] font-['Lufga:Medium',_sans-serif] text-[14px]"
                whileHover={{ x: 5 }}
              >
                Read more →
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </motion.section>

      {/* Testimonials Section - Completely redone */}
      <section id="testimonials" className="absolute top-[2880px] w-full left-[22px]">
        <motion.div
          className="absolute font-['Playfair_Display:Regular',_sans-serif] font-normal leading-[81px] text-[#000000] text-[81.308px] text-left text-nowrap top-0"
          style={{ left: "calc(20% + 50px)" }}
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <p className="whitespace-pre">Testimonials</p>
        </motion.div>

        {/* First testimonial card */}
        <motion.div
          className="absolute top-[150px] left-0"
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <div className="bg-[#f9cec9] rounded-[20px] w-[400px] overflow-hidden cursor-pointer">
            <motion.div
              className="flex h-[280px] items-center justify-center w-full"
              whileHover={{ scale: 1.05 }}
            >
              <div className="flex-none rotate-[270deg]">
                <div
                  className="h-[300px] w-[280px] bg-center bg-cover bg-no-repeat"
                  style={{ backgroundImage: `url('${imgRectangle31}')` }}
                />
              </div>
            </motion.div>
            
            <div className="p-6">
              <motion.h3 
                className="font-['Inter:Semi_Bold',_sans-serif] font-semibold text-[20px] text-[#000000] mb-3"
                whileHover={{ scale: 1.05 }}
              >
                "Amazing Team with Great Results"
              </motion.h3>
              
              <p className="font-['Inter:Light',_sans-serif] font-light text-[14px] text-[#000000] mb-4 leading-[20px]">
                Working with Julia was an incredible experience. Her attention to detail and creative vision transformed our project beyond expectations.
              </p>
              
              <motion.button
                className="font-['Inter:Medium',_sans-serif] font-medium text-[14px] text-[#000000] hover:text-[#feb273] transition-colors duration-300"
                whileHover={{ x: 5 }}
              >
                See full review →
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Second testimonial card */}
        <motion.div
          className="absolute top-[150px]"
          style={{ left: "calc(50% - 100px)" }}
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <div className="bg-[#181818] rounded-[20px] w-[400px] overflow-hidden cursor-pointer">
            <motion.div
              className="flex h-[280px] items-center justify-center w-full"
              whileHover={{ scale: 1.05 }}
            >
              <div className="flex-none rotate-[270deg]">
                <div
                  className="h-[300px] w-[280px] bg-center bg-cover bg-no-repeat"
                  style={{ backgroundImage: `url('${imgRectangle28}')` }}
                />
              </div>
            </motion.div>
            
            <div className="p-6">
              <motion.h3 
                className="font-['Inter:Semi_Bold',_sans-serif] font-semibold text-[20px] text-[#ffffff] mb-3"
                whileHover={{ scale: 1.05 }}
              >
                "Innovative Design Solutions"
              </motion.h3>
              
              <p className="font-['Inter:Light',_sans-serif] font-light text-[14px] text-[#ffffff] mb-4 leading-[20px]">
                Julia's innovative approach to design and her ability to integrate AI technology seamlessly impressed our entire team.
              </p>
              
              <motion.button
                className="font-['Inter:Medium',_sans-serif] font-medium text-[14px] text-[#ffffff] hover:text-[#feb273] transition-colors duration-300"
                whileHover={{ x: 5 }}
              >
                See full review →
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Third testimonial card */}
        <motion.div
          className="absolute top-[500px] left-[200px]"
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <div className="bg-[#181818] rounded-[20px] w-[400px] overflow-hidden cursor-pointer">
            <motion.div
              className="flex h-[280px] items-center justify-center w-full"
              whileHover={{ scale: 1.05 }}
            >
              <div className="flex-none rotate-[270deg]">
                <div
                  className="h-[300px] w-[280px] bg-center bg-cover bg-no-repeat"
                  style={{ backgroundImage: `url('${imgRectangle26}')` }}
                />
              </div>
            </motion.div>
            
            <div className="p-6">
              <motion.h3 
                className="font-['Inter:Semi_Bold',_sans-serif] font-semibold text-[20px] text-[#ffffff] mb-3"
                whileHover={{ scale: 1.05 }}
              >
                "Exceptional Creative Vision"
              </motion.h3>
              
              <p className="font-['Inter:Light',_sans-serif] font-light text-[14px] text-[#ffffff] mb-4 leading-[20px]">
                The creative vision and technical expertise Julia brought to our project resulted in something truly extraordinary.
              </p>
              
              <motion.button
                className="font-['Inter:Medium',_sans-serif] font-medium text-[14px] text-[#ffffff] hover:text-[#feb273] transition-colors duration-300"
                whileHover={{ x: 5 }}
              >
                See full review →
              </motion.button>
            </div>
          </div>
        </motion.div>
      </section>
    </div>
  );
}