import { PortfolioItem } from '@/types/portfolio';

export const portfolioItems: PortfolioItem[] = [
  {
    id: 'hero-text',
    type: 'hero-text',
    gridArea: 'hero-text',
    className: 'bg-[#fadcd9]',
    content: {
      title: 'Artist Redefining',
      subtitle: 'Architecture',
      subtitleSuffix: 'with AI-Driven Design',
      textColor: 'text-black',
      hasFlowerIcon: true,
    }
  },
  {
    id: 'profile',
    type: 'image',
    gridArea: 'profile',
    className: 'bg-orange-200',
    content: {
      image: '/img/layput_card-2.png',
      title: '<PERSON>',
    }
  },
  {
    id: 'work-showcase',
    type: 'work-showcase',
    gridArea: 'work-showcase',
    className: 'bg-[#fadcd9]',
    content: {
      title: 'Musea',
      mainImage: '/img/layput_card-3.png',
      projectList: ['Elara', 'Verve', 'Zephyr'],
      hasArrow: true,
      textColor: 'text-black',
      socialFrame: {
        title: 'Social Projects',
        subtitle: 'Community driven',
        hasArrow: true
      }
    }
  },
  {
    id: 'about-card',
    type: 'about',
    gridArea: 'about-card',
    className: 'bg-[#fadcd9]',
    content: {
      title: 'PER PIXEL',
      description: 'Julia Huang is an innovative AI artist, renowned for blending cutting-edge technology with creative expression. Based in LA, she crafts unique digital art experiences accessible globally.',
      textColor: 'text-black',
      hasCircleIcon: true,
    }
  },
  {
    id: 'contact-card',
    type: 'contact',
    gridArea: 'contact-card',
    className: 'bg-[#f8afa6]',
    content: {
      title: 'Contact me',
      subtitle: 'Have some questions?',
      hasArrow: true,
      link: '/contact',
      textColor: 'text-black',
    }
  },
  {
    id: 'social-links',
    type: 'social',
    gridArea: 'social-links',
    className: 'bg-[#fadcd9]',
    content: {
      socialLinks: [
        { platform: 'Instagram', url: 'https://instagram.com', label: 'INSTAGRAM' },
        { platform: 'Twitter', url: 'https://twitter.com', label: 'TWITTER' },
        { platform: 'LinkedIn', url: 'https://linkedin.com', label: 'LINKEDIN' },
      ],
      textColor: 'text-gray-700',
    }
  }
];

export const gridConfig = {
  columns: 12,
  rows: 6,
  gap: '16px',
  areas: [
    'hero-text hero-text hero-text hero-text hero-text profile profile profile work-showcase work-showcase work-showcase work-showcase',
    'hero-text hero-text hero-text hero-text hero-text profile profile profile work-showcase work-showcase work-showcase work-showcase',
    'hero-text hero-text hero-text hero-text hero-text profile profile profile work-showcase work-showcase work-showcase work-showcase',
    'about-card about-card about-card about-card contact-card contact-card contact-card contact-card work-showcase work-showcase work-showcase work-showcase',
    'about-card about-card about-card about-card contact-card contact-card contact-card contact-card work-showcase work-showcase work-showcase work-showcase',
    'social-links social-links social-links social-links social-links social-links social-links social-links social-links social-links social-links social-links'
  ]
};
