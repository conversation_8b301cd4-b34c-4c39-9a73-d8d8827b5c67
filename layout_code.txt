// Social Media Icons Components
function InstagramIcon() {
  return (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
    </svg>
  );
}

function LinkedInIcon() {
  return (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
    </svg>
  );
}

function TwitterIcon() {
  return (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
    </svg>
  );
}

function SocialMediaButton({ icon, platform, delay = 0 }) {
  const handleClick = () => {
    // Handle social media navigation
    const urls = {
      instagram: 'https://instagram.com',
      linkedin: 'https://linkedin.com',
      twitter: 'https://twitter.com'
    };
    window.open(urls[platform], '_blank');
  };

  return (
    <motion.button
      className="bg-[rgba(255,255,255,0.2)] backdrop-blur-sm rounded-full p-3 hover:bg-[rgba(255,255,255,0.3)] transition-all duration-300 cursor-pointer group"
      initial={{ scale: 0, opacity: 0 }}
      whileInView={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.5, delay, type: "spring", stiffness: 200 }}
      whileHover={{ scale: 1.1, y: -2 }}
      whileTap={{ scale: 0.95 }}
      onClick={handleClick}
      viewport={{ once: true }}
    >
      <div className="text-[#000000] group-hover:text-[#feb273] transition-colors duration-300">
        {icon}
      </div>
    </motion.button>
  );
}

export default function InteractiveDigitalAgency() {
  const [currentSection, setCurrentSection] = useState('hero');

  useEffect(() => {
    const handleScroll = () => {
      const sections = ['hero', 'about', 'our-work', 'contact', 'blog'];
      const scrollPosition = window.scrollY + window.innerHeight / 2;

      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setCurrentSection(section);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div
      className="bg-[#ffffff] relative size-full"
      data-name="Digital Agency Website"
    >
      <HeroSection />
      
      {/* About Section */}
      <section id="about" className="absolute contents left-[22px] top-[760px]" data-name="Section 2">
        {/* Slogan/Intro */}
        <motion.div
          className="absolute bg-[#fadcd9] h-[479px] left-[22px] overflow-hidden rounded-[20px] top-[760px] w-[565px]"
          data-name="SLOGAN/INTRO"
          initial={{ x: -50, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="absolute font-['Gilroy:Bold',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[56px] text-left w-[475px]"
            style={{ top: "calc(50% - 50px)" }}
            whileHover={{ scale: 1.02 }}
          >
            <p className="leading-[60px]">
              <span>Artist Redefining </span>
              <motion.span 
                className="font-['Gilroy:Light_Italic',_sans-serif] italic"
                animate={{ color: ["#000000", "#feb273", "#000000"] }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                Architecture
              </motion.span>
              <span> with AI-Driven Design</span>
            </p>
          </motion.div>
          
          <motion.div
            className="absolute right-6 size-[119px] top-[34px]"
            data-name="FLOWER ICON"
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
          >
            <svg
              className="block size-full"
              fill="none"
              preserveAspectRatio="none"
              viewBox="0 0 119 119"
            >
              <g clipPath="url(#clip0_1_366)" id="FLOWER ICON">
                <path
                  d={svgPaths.p12a08980}
                  fill="var(--fill-0, #F8AFA6)"
                  id="Vector"
                />
              </g>
              <defs>
                <clipPath id="clip0_1_366">
                  <rect fill="white" height="119" width="119" />
                </clipPath>
              </defs>
            </svg>
          </motion.div>
        </motion.div>

        {/* Portrait */}
        <motion.div
          className="absolute h-[476px] overflow-hidden rounded-[20px] top-[763px] w-[330px]"
          data-name="PORTRAIT"
          style={{ left: "calc(41.667% + 12px)" }}
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <div
            className="absolute bg-center bg-cover bg-no-repeat h-[556px] left-[-25px] top-[-19px] w-[355px]"
            data-name="IMAGE"
            style={{ backgroundImage: `url('${imgImage}')` }}
          />
        </motion.div>

        {/* Work Section */}
        <motion.div
          id="our-work"
          className="absolute bg-[#fadcd9] h-[726px] overflow-hidden rounded-[20px] top-[763px] w-[447px]"
          data-name="WORK"
          style={{ left: "calc(66.667% + 6px)" }}
          initial={{ x: 50, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          {/* Work Title */}
          <motion.div
            className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-6"
            initial={{ y: 10, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <p className="block leading-[normal] whitespace-pre">Musea</p>
          </motion.div>

          {/* Main Project Image */}
          <motion.div
            className="absolute h-[269px] left-6 overflow-hidden rounded-[12px] top-[60px] w-[399px]"
            initial={{ scale: 0.95, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            whileHover={{ scale: 1.02 }}
            viewport={{ once: true }}
          >
            <div
              className="h-[400px] w-[399px] bg-center bg-cover bg-no-repeat"
              style={{
                backgroundImage: `url('${imgLilcodermanPinkChairSittingOnTableInARoomInTheStyle8F5E5Aa938F84Af089F38572B0Ae93621}')`,
                backgroundPosition: 'center -113px'
              }}
            />
          </motion.div>

          {/* Other Projects */}
          <motion.div
            className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-[360px]"
            initial={{ x: -10, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            whileHover={{ color: "#feb273" }}
            viewport={{ once: true }}
          >
            <p className="block leading-[normal] whitespace-pre cursor-pointer">Elara</p>
          </motion.div>

          <motion.div
            className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-[420px]"
            initial={{ x: -10, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            whileHover={{ color: "#feb273" }}
            viewport={{ once: true }}
          >
            <p className="block leading-[normal] whitespace-pre cursor-pointer">Verve</p>
          </motion.div>

          <motion.div
            className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-[480px]"
            initial={{ x: -10, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 1.0 }}
            whileHover={{ color: "#feb273" }}
            viewport={{ once: true }}
          >
            <p className="block leading-[normal] whitespace-pre cursor-pointer">Zephyr</p>
          </motion.div>

          {/* Updated Social Frame - With Instagram, LinkedIn, Twitter */}
          <motion.div
            className="absolute bottom-6 left-6 right-6"
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 1.2 }}
            viewport={{ once: true }}
          >
            <div className="bg-[rgba(255,255,255,0.2)] backdrop-blur-sm rounded-[16px] p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <p className="text-[#000000] font-['Lufga:Medium',_sans-serif] text-[14px] mb-1">Social Projects</p>
                  <p className="text-[#666666] font-['Lufga:Light',_sans-serif] text-[11px]">Follow our journey</p>
                </div>
              </div>
              
              {/* Social Media Icons Row */}
              <div className="flex items-center gap-3">
                <SocialMediaButton 
                  icon={<InstagramIcon />} 
                  platform="instagram" 
                  delay={0.1}
                />
                <SocialMediaButton 
                  icon={<LinkedInIcon />} 
                  platform="linkedin" 
                  delay={0.2}
                />
                <SocialMediaButton 
                  icon={<TwitterIcon />} 
                  platform="twitter" 
                  delay={0.3}
                />
                
                <motion.div
                  className="ml-auto text-[#feb273] cursor-pointer font-['Lufga:Medium',_sans-serif] text-[12px]"
                  whileHover={{ x: 3 }}
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  Connect →
                </motion.div>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* About */}
        <motion.div
          className="absolute bg-[#fadcd9] h-[351px] left-[22px] overflow-hidden rounded-[20px] top-[1263px] w-[448px]"
          data-name="ABOUT"
          initial={{ scale: 0.9, opacity: 0 }}
          whileInView={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <div
            className="absolute font-['Gilroy:Light',_sans-serif] leading-[25px] left-6 not-italic text-[#000000] text-[20px] text-left w-[296px]"
            style={{ top: "calc(50% - 20.5px)" }}
          >
            <p>
              Julia Huang is an innovative AI artist, renowned for blending
              cutting-edge technology with creative expression. Based in LA, she
              crafts unique digital art experiences accessible globally.
            </p>
          </div>
          
          <div className="absolute left-6 size-[38px] top-8" data-name="CIRCLE ICON">
            <svg
              className="block size-full"
              fill="none"
              preserveAspectRatio="none"
              viewBox="0 0 38 38"
            >
              <g clipPath="url(#clip0_1_396)" id="CIRCLE ICON">
                <motion.path
                  d={svgPaths.p21c04900}
                  fill="var(--fill-0, #F8AFA6)"
                  id="Vector"
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                />
              </g>
              <defs>
                <clipPath id="clip0_1_396">
                  <rect fill="white" height="38" width="38" />
                </clipPath>
              </defs>
            </svg>
          </div>
        </motion.div>

        {/* Contact Section - Fixed with rounded corners */}
        <section id="contact" className="absolute contents">
          <motion.div
            className="absolute bg-[#f8afa6] h-[351px] overflow-hidden rounded-[20px] top-[1263px] w-[448px]"
            data-name="CONTACT"
            style={{ left: "calc(33.333% + 14px)" }}
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6 }}
            whileHover={{ scale: 1.02, backgroundColor: "#f6a194" }}
            viewport={{ once: true }}
          >
            <motion.div 
              className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[55px] text-left text-nowrap top-[259px]"
              whileHover={{ color: "#ffffff" }}
              transition={{ duration: 0.3 }}
            >
              <p className="block leading-[normal] whitespace-pre">Contact me</p>
            </motion.div>
            
            <div className="absolute box-border content-stretch flex flex-row items-end justify-between left-1/2 p-0 top-[30px] translate-x-[-50%] w-[400px]">
              <div className="font-['Gilroy:Light',_sans-serif] leading-[normal] not-italic relative shrink-0 text-[#000000] text-[15px] text-left text-nowrap whitespace-pre">
                <p className="block mb-0">Have some</p>
                <p className="block">questions?</p>
              </div>
              <motion.div
                className="relative shrink-0 size-[38px] cursor-pointer"
                whileHover={{ scale: 1.2, rotate: 45 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg
                  className="block size-full"
                  fill="none"
                  preserveAspectRatio="none"
                  viewBox="0 0 38 38"
                >
                  <g id="ARROW">
                    <path
                      clipRule="evenodd"
                      d={svgPaths.p17200c00}
                      fill="var(--fill-0, black)"
                      fillRule="evenodd"
                      id="Vector"
                    />
                  </g>
                </svg>
              </motion.div>
            </div>
          </motion.div>
        </section>

        <motion.div 
          className="absolute font-['Inter:Black',_sans-serif] font-black h-[77px] leading-[77px] left-[46px] not-italic text-[#131d26] text-[78px] text-left top-[1316px] tracking-[1.56px] w-[430px]"
          initial={{ x: -100, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 1 }}
          viewport={{ once: true }}
        >
          <p className="adjustLetterSpacing">PER PIXEL</p>
        </motion.div>
      </section>