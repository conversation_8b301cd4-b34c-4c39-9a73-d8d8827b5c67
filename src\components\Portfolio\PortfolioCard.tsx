"use client";
import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { PortfolioItem } from '@/types/portfolio';

interface PortfolioCardProps {
  item: PortfolioItem;
}

const ArrowIcon = () => (
  <svg
    className="w-6 h-6 transition-transform group-hover:translate-x-1 group-hover:-translate-y-1"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17L17 7M17 7H7M17 7V17" />
  </svg>
);

const FlowerIcon = () => (
  <motion.div
    className="absolute right-2 md:right-6 w-[60px] h-[60px] md:w-[80px] md:h-[80px] lg:w-[119px] lg:h-[119px] top-4 md:top-[34px]"
    animate={{ rotate: [0, 360] }}
    transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
  >
    <svg
      className="block w-full h-full"
      fill="none"
      preserveAspectRatio="none"
      viewBox="0 0 119 119"
    >
      <g clipPath="url(#clip0_1_366)">
        <path
          d="M59.5 59.5m-50 0a50 50 0 1 0 100 0a50 50 0 1 0 -100 0"
          fill="#F8AFA6"
          opacity="0.6"
        />
      </g>
      <defs>
        <clipPath id="clip0_1_366">
          <rect fill="white" height="119" width="119" />
        </clipPath>
      </defs>
    </svg>
  </motion.div>
);

const CircleIcon = () => (
  <motion.div
    className="absolute left-6 w-[38px] h-[38px] top-8"
    animate={{ rotate: [0, 360] }}
    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
  >
    <svg
      className="block w-full h-full"
      fill="none"
      preserveAspectRatio="none"
      viewBox="0 0 38 38"
    >
      <circle cx="19" cy="19" r="15" fill="#F8AFA6" opacity="0.8" />
    </svg>
  </motion.div>
);

// Social Media Icons Components
const InstagramIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
  </svg>
);

const LinkedInIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
  </svg>
);

const TwitterIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

const SocialMediaButton: React.FC<{ icon: React.ReactNode; platform: string; delay?: number }> = ({ icon, platform, delay = 0 }) => {
  const handleClick = () => {
    const urls = {
      instagram: 'https://instagram.com',
      linkedin: 'https://linkedin.com',
      twitter: 'https://twitter.com'
    };
    window.open(urls[platform as keyof typeof urls], '_blank');
  };

  return (
    <motion.button
      className="bg-[rgba(255,255,255,0.2)] backdrop-blur-sm rounded-full p-3 hover:bg-[rgba(255,255,255,0.3)] transition-all duration-300 cursor-pointer group"
      initial={{ scale: 0, opacity: 0 }}
      whileInView={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.5, delay, type: "spring", stiffness: 200 }}
      whileHover={{ scale: 1.1, y: -2 }}
      whileTap={{ scale: 0.95 }}
      onClick={handleClick}
      viewport={{ once: true }}
    >
      <div className="text-[#000000] group-hover:text-[#feb273] transition-colors duration-300">
        {icon}
      </div>
    </motion.button>
  );
};

const HeroTextCard: React.FC<{ item: PortfolioItem }> = ({ item }) => (
  <motion.div
    className={`${item.className} overflow-hidden rounded-[20px] h-full relative`}
    data-name="SLOGAN/INTRO"
    initial={{ x: -50, opacity: 0 }}
    whileInView={{ x: 0, opacity: 1 }}
    transition={{ duration: 0.8 }}
    viewport={{ once: true }}
  >
    <motion.div
      className={`absolute ${item.content.fontFamily} leading-[0] left-6 not-italic ${item.content.textColor} ${item.content.fontSize} text-left w-[475px]`}
      style={{ top: "calc(50% - 50px)" }}
      whileHover={{ scale: 1.02 }}
    >
      <p className={item.content.lineHeight}>
        <span>{item.content.title} </span>
        <motion.span
          className="font-['Gilroy:Light_Italic',_sans-serif] italic"
          animate={{ color: ["#000000", "#feb273", "#000000"] }}
          transition={{ duration: 3, repeat: Infinity }}
        >
          {item.content.subtitle}
        </motion.span>
        <span> {item.content.subtitleSuffix}</span>
      </p>
    </motion.div>

    {/* AI-Driven Vector Image */}
    <motion.div
      className="absolute right-6 top-6 w-[120px] h-[120px]"
      initial={{ scale: 0.8, opacity: 0 }}
      whileInView={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.6, delay: 0.3 }}
      whileHover={{ scale: 1.05 }}
      viewport={{ once: true }}
    >
      <img
        src="/img/AI-Driven_Card-Vector.png"
        alt="AI-Driven Design"
        className="w-full h-full object-contain"
      />
    </motion.div>
  </motion.div>
);

const ImageCard: React.FC<{ item: PortfolioItem }> = ({ item }) => (
  <motion.div
    className="overflow-hidden rounded-[20px] h-full relative"
    data-name="PORTRAIT"
    initial={{ y: 50, opacity: 0 }}
    whileInView={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.8, delay: 0.3 }}
    whileHover={{ scale: 1.02 }}
    viewport={{ once: true }}
  >
    {item.content.image && (
      <div
        className="absolute bg-center bg-cover bg-no-repeat h-[556px] left-[-25px] top-[-19px] w-[355px]"
        data-name="IMAGE"
        style={{ backgroundImage: `url('${item.content.image}')` }}
      />
    )}


  </motion.div>
);

const WorkShowcaseCard: React.FC<{ item: PortfolioItem }> = ({ item }) => (
  <motion.div
    className={`${item.className} overflow-hidden rounded-[20px] h-full relative`}
    data-name="WORK"
    initial={{ x: 50, opacity: 0 }}
    whileInView={{ x: 0, opacity: 1 }}
    transition={{ duration: 0.8 }}
    viewport={{ once: true }}
  >
    {/* Work Title */}
    <motion.div
      className={`absolute ${item.content.fontFamily} leading-[0] left-6 not-italic ${item.content.textColor} ${item.content.fontSize} text-left text-nowrap top-6`}
      initial={{ y: 10, opacity: 0 }}
      whileInView={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      viewport={{ once: true }}
    >
      <p className="block leading-[normal] whitespace-pre">{item.content.title}</p>
    </motion.div>

    {/* Main Project Image */}
    {item.content.mainImage && (
      <motion.div
        className="absolute h-[269px] left-6 overflow-hidden rounded-[12px] top-[60px] w-[399px]"
        initial={{ scale: 0.95, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        whileHover={{ scale: 1.02 }}
        viewport={{ once: true }}
      >
        <div
          className="h-[400px] w-[399px] bg-center bg-cover bg-no-repeat"
          style={{
            backgroundImage: `url('${item.content.mainImage}')`,
            backgroundPosition: 'center -113px'
          }}
        />
      </motion.div>
    )}

    {/* Other Projects */}
    {item.content.projectList && item.content.projectList.map((project, index) => (
      <motion.div
        key={project}
        className={`absolute ${item.content.fontFamily} leading-[0] left-6 not-italic ${item.content.textColor} ${item.content.fontSize} text-left text-nowrap`}
        style={{ top: `${360 + index * 60}px` }}
        initial={{ x: -10, opacity: 0 }}
        whileInView={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.6 + index * 0.2 }}
        whileHover={{ color: "#feb273" }}
        viewport={{ once: true }}
      >
        <p className="block leading-[normal] whitespace-pre cursor-pointer">{project}</p>
      </motion.div>
    ))}

    {/* Social Frame */}
    {item.content.socialFrame && (
      <motion.div
        className="absolute bottom-6 left-6 right-6"
        initial={{ y: 20, opacity: 0 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 1.2 }}
        viewport={{ once: true }}
      >
        <div className="bg-[rgba(255,255,255,0.2)] backdrop-blur-sm rounded-[16px] p-4">
          <div className="flex items-center justify-between mb-3">
            <div>
              <p className="text-[#000000] font-['Lufga:Medium',_sans-serif] text-[14px] mb-1">{item.content.socialFrame.title}</p>
              <p className="text-[#666666] font-['Lufga:Light',_sans-serif] text-[11px]">{item.content.socialFrame.subtitle}</p>
            </div>
          </div>

          {/* Social Media Icons Row */}
          <div className="flex items-center gap-3">
            <SocialMediaButton
              icon={<InstagramIcon />}
              platform="instagram"
              delay={0.1}
            />
            <SocialMediaButton
              icon={<LinkedInIcon />}
              platform="linkedin"
              delay={0.2}
            />
            <SocialMediaButton
              icon={<TwitterIcon />}
              platform="twitter"
              delay={0.3}
            />

            <motion.div
              className="ml-auto text-[#feb273] cursor-pointer font-['Lufga:Medium',_sans-serif] text-[12px]"
              whileHover={{ x: 3 }}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              Connect →
            </motion.div>
          </div>
        </div>
      </motion.div>
    )}
  </motion.div>
);

const AboutCard: React.FC<{ item: PortfolioItem }> = ({ item }) => (
  <motion.div
    className={`${item.className} overflow-hidden rounded-[20px] h-full relative`}
    data-name="ABOUT"
    initial={{ scale: 0.9, opacity: 0 }}
    whileInView={{ scale: 1, opacity: 1 }}
    transition={{ duration: 0.6 }}
    whileHover={{ scale: 1.02 }}
    viewport={{ once: true }}
  >
    {/* Background Image */}
    <div
      className="absolute inset-0 bg-center bg-cover bg-no-repeat opacity-30"
      style={{ backgroundImage: `url('/img/PER PIXEL_Layout-Vector.png')` }}
    />

    <div
      className={`absolute ${item.content.fontFamily} ${item.content.lineHeight} left-6 not-italic ${item.content.textColor} ${item.content.fontSize} text-left w-[296px] z-10`}
      style={{ top: "calc(50% - 20.5px)" }}
    >
      <p>
        {item.content.description}
      </p>
    </div>

    {/* PER PIXEL Brand Text */}
    <motion.div
      className="absolute bottom-6 left-6 z-10"
      initial={{ x: -100, opacity: 0 }}
      whileInView={{ x: 0, opacity: 1 }}
      transition={{ duration: 1, delay: 0.5 }}
      viewport={{ once: true }}
    >
      <div className="font-['Inter:Black',_sans-serif] font-black text-[#131d26] text-[48px] leading-[48px] tracking-[1.56px]">
        <p className="adjustLetterSpacing">PER PIXEL</p>
      </div>
    </motion.div>
  </motion.div>
);

const ContactCard: React.FC<{ item: PortfolioItem }> = ({ item }) => (
  <motion.div
    className={`${item.className} overflow-hidden rounded-[20px] h-full relative`}
    data-name="CONTACT"
    initial={{ scale: 0.9, opacity: 0 }}
    whileInView={{ scale: 1, opacity: 1 }}
    transition={{ duration: 0.6 }}
    whileHover={{ scale: 1.02, backgroundColor: "#f6a194" }}
    viewport={{ once: true }}
  >
    <motion.div
      className={`absolute ${item.content.titleFontFamily} leading-[0] left-6 not-italic ${item.content.textColor} ${item.content.titleFontSize} text-left text-nowrap top-[259px]`}
      whileHover={{ color: "#ffffff" }}
      transition={{ duration: 0.3 }}
    >
      <p className="block leading-[normal] whitespace-pre">{item.content.title}</p>
    </motion.div>

    <div className="absolute box-border content-stretch flex flex-row items-end justify-between left-1/2 p-0 top-[30px] translate-x-[-50%] w-[400px]">
      <div className={`${item.content.subtitleFontFamily} leading-[normal] not-italic relative shrink-0 ${item.content.textColor} ${item.content.subtitleFontSize} text-left text-nowrap whitespace-pre`}>
        <p className="block mb-0">Have some</p>
        <p className="block">questions?</p>
      </div>
      {item.content.hasArrow && (
        <motion.div
          className="relative shrink-0 size-[38px] cursor-pointer"
          whileHover={{ scale: 1.2, rotate: 45 }}
          whileTap={{ scale: 0.9 }}
        >
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 38 38"
          >
            <g id="PLUS_CROSS">
              <path
                d="M19 4L19 34M4 19L34 19"
                stroke="black"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </g>
          </svg>
        </motion.div>
      )}
    </div>
  </motion.div>
);



const SocialCard: React.FC<{ item: PortfolioItem }> = ({ item }) => (
  <motion.div
    className={`${item.className} overflow-hidden rounded-[20px] h-full relative`}
    data-name="SOCIALS"
    initial={{ y: 20, opacity: 0 }}
    whileInView={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.6, delay: 1.2 }}
    viewport={{ once: true }}
  >
    <div className={`absolute box-border content-stretch flex flex-row ${item.content.fontFamily} items-center justify-between leading-[0] left-1/2 not-italic p-0 ${item.content.textColor} ${item.content.fontSize} text-left text-nowrap top-1/2 translate-x-[-50%] translate-y-[-50%] uppercase w-[334px]`}>
      {item.content.socialLinks?.map((social, index) => (
        <motion.div
          key={index}
          className="relative shrink-0"
          whileHover={{ y: -2 }}
        >
          <p className="block leading-[normal] text-nowrap whitespace-pre">
            {social.label}
          </p>
        </motion.div>
      ))}
    </div>
  </motion.div>
);

const PortfolioCard: React.FC<PortfolioCardProps> = ({ item }) => {
  const cardComponents = {
    'hero-text': HeroTextCard,
    image: ImageCard,
    'work-showcase': WorkShowcaseCard,
    about: AboutCard,
    contact: ContactCard,
    social: SocialCard,
  };

  const CardComponent = cardComponents[item.type];

  return CardComponent ? <CardComponent item={item} /> : null;
};

export default PortfolioCard;
