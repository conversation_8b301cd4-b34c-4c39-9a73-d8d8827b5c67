"use client";
import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { PortfolioItem } from '@/types/portfolio';

interface PortfolioCardProps {
  item: PortfolioItem;
}

const ArrowIcon = () => (
  <svg
    className="w-6 h-6 transition-transform group-hover:translate-x-1 group-hover:-translate-y-1"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17L17 7M17 7H7M17 7V17" />
  </svg>
);

const FlowerIcon = () => (
  <motion.div
    className="absolute right-2 md:right-6 w-[60px] h-[60px] md:w-[80px] md:h-[80px] lg:w-[119px] lg:h-[119px] top-4 md:top-[34px]"
    animate={{ rotate: [0, 360] }}
    transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
  >
    <svg
      className="block w-full h-full"
      fill="none"
      preserveAspectRatio="none"
      viewBox="0 0 119 119"
    >
      <g clipPath="url(#clip0_1_366)">
        <path
          d="M59.5 59.5m-50 0a50 50 0 1 0 100 0a50 50 0 1 0 -100 0"
          fill="#F8AFA6"
          opacity="0.6"
        />
      </g>
      <defs>
        <clipPath id="clip0_1_366">
          <rect fill="white" height="119" width="119" />
        </clipPath>
      </defs>
    </svg>
  </motion.div>
);

const CircleIcon = () => (
  <motion.div
    className="absolute left-6 w-[38px] h-[38px] top-8"
    animate={{ rotate: [0, 360] }}
    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
  >
    <svg
      className="block w-full h-full"
      fill="none"
      preserveAspectRatio="none"
      viewBox="0 0 38 38"
    >
      <circle cx="19" cy="19" r="15" fill="#F8AFA6" opacity="0.8" />
    </svg>
  </motion.div>
);

const HeroTextCard: React.FC<{ item: PortfolioItem }> = ({ item }) => (
  <motion.div
    className={`${item.className} p-4 md:p-6 rounded-[20px] h-full flex flex-col justify-center relative overflow-hidden group`}
    initial={{ x: -50, opacity: 0 }}
    whileInView={{ x: 0, opacity: 1 }}
    transition={{ duration: 0.8 }}
    viewport={{ once: true }}
    whileHover={{ scale: 1.02 }}
  >
    {item.content.hasFlowerIcon && <FlowerIcon />}

    <motion.div
      className={`${item.content.textColor} space-y-2 max-w-[475px]`}
      style={{ fontFamily: "'Gilroy', sans-serif" }}
    >
      <h2 className="text-[28px] sm:text-[40px] lg:text-[56px] font-bold leading-[1.1] lg:leading-[60px]">
        <span>{item.content.title} </span>
        <motion.span
          className="font-light italic"
          animate={{ color: ["#000000", "#feb273", "#000000"] }}
          transition={{ duration: 3, repeat: Infinity }}
        >
          {item.content.subtitle}
        </motion.span>
        <span> {item.content.subtitleSuffix}</span>
      </h2>
    </motion.div>
  </motion.div>
);

const ImageCard: React.FC<{ item: PortfolioItem }> = ({ item }) => (
  <motion.div
    className={`${item.className} rounded-[20px] h-full overflow-hidden relative group`}
    initial={{ y: 50, opacity: 0 }}
    whileInView={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.8, delay: 0.3 }}
    whileHover={{ scale: 1.02 }}
    viewport={{ once: true }}
  >
    {item.content.image && (
      <Image
        src={item.content.image}
        alt={item.content.title || 'Portfolio image'}
        fill
        className="object-cover transition-transform duration-300 group-hover:scale-105"
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        priority={false}
        loading="lazy"
        quality={85}
      />
    )}
  </motion.div>
);

const WorkShowcaseCard: React.FC<{ item: PortfolioItem }> = ({ item }) => (
  <motion.div
    className={`${item.className} p-4 md:p-6 rounded-[20px] h-full flex flex-col group cursor-pointer`}
    initial={{ x: 50, opacity: 0 }}
    whileInView={{ x: 0, opacity: 1 }}
    transition={{ duration: 0.8 }}
    viewport={{ once: true }}
    role="button"
    tabIndex={0}
    aria-label={`View ${item.content.title} project`}
  >
    {/* Work Title */}
    <motion.div
      className={`font-medium text-[18px] md:text-[22px] lg:text-[25px] ${item.content.textColor} mb-4`}
      style={{ fontFamily: "'Gilroy', sans-serif" }}
      initial={{ y: 10, opacity: 0 }}
      whileInView={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      viewport={{ once: true }}
    >
      <p>{item.content.title}</p>
    </motion.div>

    {/* Main Project Image */}
    {item.content.mainImage && (
      <motion.div
        className="h-[180px] md:h-[220px] lg:h-[269px] mb-4 md:mb-6 overflow-hidden rounded-[12px] relative"
        initial={{ scale: 0.95, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        whileHover={{ scale: 1.02 }}
        viewport={{ once: true }}
      >
        <Image
          src={item.content.mainImage}
          alt={`${item.content.title} project`}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, 50vw"
          loading="lazy"
          quality={85}
        />
      </motion.div>
    )}

    {/* Project List */}
    {item.content.projectList && (
      <div className="space-y-4 md:space-y-6 mb-4 md:mb-6">
        {item.content.projectList.map((project, index) => (
          <motion.div
            key={project}
            className={`font-medium text-[18px] md:text-[22px] lg:text-[25px] ${item.content.textColor} cursor-pointer hover:text-[#feb273] transition-colors`}
            style={{ fontFamily: "'Gilroy', sans-serif" }}
            initial={{ x: -10, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 + index * 0.2 }}
            viewport={{ once: true }}
          >
            <p>{project}</p>
          </motion.div>
        ))}
      </div>
    )}

    {/* Social Frame */}
    {item.content.socialFrame && (
      <motion.div
        className="mt-auto"
        initial={{ y: 20, opacity: 0 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 1.2 }}
        viewport={{ once: true }}
      >
        <div className="bg-[rgba(255,255,255,0.3)] backdrop-blur-sm rounded-[12px] p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-[#f8afa6] rounded-full p-2">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18.77 7.46H5.23l-.43-2.57A1 1 0 0 0 3.82 4H2a1 1 0 0 0 0 2h1.18l2.62 15.56a1 1 0 0 0 1 .84h9.4a1 1 0 0 0 1-.84L20.18 6h-1.41z" />
                </svg>
              </div>
              <div>
                <p className="text-[#000000] font-medium text-[12px]" style={{ fontFamily: "'Lufga', sans-serif" }}>
                  {item.content.socialFrame.title}
                </p>
                <p className="text-[#666666] font-light text-[10px]" style={{ fontFamily: "'Lufga', sans-serif" }}>
                  {item.content.socialFrame.subtitle}
                </p>
              </div>
            </div>
            {item.content.socialFrame.hasArrow && (
              <motion.div
                className="text-[#feb273] cursor-pointer"
                whileHover={{ x: 3 }}
              >
                →
              </motion.div>
            )}
          </div>
        </div>
      </motion.div>
    )}
  </motion.div>
);

const AboutCard: React.FC<{ item: PortfolioItem }> = ({ item }) => (
  <motion.div
    className={`${item.className} p-4 md:p-6 rounded-[20px] h-full flex flex-col justify-between relative`}
    initial={{ scale: 0.9, opacity: 0 }}
    whileInView={{ scale: 1, opacity: 1 }}
    transition={{ duration: 0.6 }}
    whileHover={{ scale: 1.02 }}
    viewport={{ once: true }}
  >
    {item.content.hasCircleIcon && <CircleIcon />}

    <div className="mt-auto">
      <h2 className={`text-[32px] md:text-[56px] lg:text-[78px] font-black ${item.content.textColor} mb-4 tracking-[1.56px]`} style={{ fontFamily: "'Inter', sans-serif" }}>
        {item.content.title}
      </h2>
      <p className={`text-[16px] md:text-[18px] lg:text-[20px] leading-[1.25] lg:leading-[25px] ${item.content.textColor} max-w-[296px]`} style={{ fontFamily: "'Gilroy', sans-serif" }}>
        {item.content.description}
      </p>
    </div>
  </motion.div>
);

const ContactCard: React.FC<{ item: PortfolioItem }> = ({ item }) => (
  <motion.div
    className={`${item.className} p-4 md:p-6 rounded-[20px] h-full flex flex-col justify-between group cursor-pointer relative`}
    initial={{ scale: 0.9, opacity: 0 }}
    whileInView={{ scale: 1, opacity: 1 }}
    transition={{ duration: 0.6 }}
    whileHover={{ scale: 1.02, backgroundColor: "#f6a194" }}
    viewport={{ once: true }}
    role="button"
    tabIndex={0}
    aria-label="Contact me"
    onKeyDown={(e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        // Handle navigation
      }
    }}
  >
    <div className="flex justify-between items-start">
      <div className={`text-[12px] md:text-[14px] lg:text-[15px] ${item.content.textColor}`} style={{ fontFamily: "'Gilroy', sans-serif" }}>
        <p>{item.content.subtitle}</p>
      </div>
      {item.content.hasArrow && (
        <motion.div
          className={`${item.content.textColor} w-[28px] h-[28px] md:w-[32px] md:h-[32px] lg:w-[38px] lg:h-[38px] cursor-pointer`}
          whileHover={{ scale: 1.2, rotate: 45 }}
          whileTap={{ scale: 0.9 }}
        >
          <svg
            className="block w-full h-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 38 38"
          >
            <path
              clipRule="evenodd"
              d="M19 4L19 34M4 19L34 19"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              fillRule="evenodd"
            />
          </svg>
        </motion.div>
      )}
    </div>

    <motion.h3
      className={`text-[28px] md:text-[42px] lg:text-[55px] font-medium ${item.content.textColor}`}
      style={{ fontFamily: "'Gilroy', sans-serif" }}
      whileHover={{ color: "#ffffff" }}
      transition={{ duration: 0.3 }}
    >
      {item.content.title}
    </motion.h3>
  </motion.div>
);

const SocialCard: React.FC<{ item: PortfolioItem }> = ({ item }) => (
  <motion.div
    className={`${item.className} p-6 rounded-[20px] h-full flex justify-center items-center space-x-8`}
    initial={{ y: 20, opacity: 0 }}
    whileInView={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.6, delay: 1.2 }}
    viewport={{ once: true }}
  >
    {item.content.socialLinks?.map((social, index) => (
      <motion.a
        key={index}
        href={social.url}
        target="_blank"
        rel="noopener noreferrer"
        className={`text-sm font-medium ${item.content.textColor} hover:opacity-70 transition-opacity`}
        whileHover={{ y: -2 }}
      >
        {social.label}
      </motion.a>
    ))}
  </motion.div>
);

const PortfolioCard: React.FC<PortfolioCardProps> = ({ item }) => {
  const cardComponents = {
    'hero-text': HeroTextCard,
    image: ImageCard,
    'work-showcase': WorkShowcaseCard,
    about: AboutCard,
    contact: ContactCard,
    social: SocialCard,
  };

  const CardComponent = cardComponents[item.type];

  return CardComponent ? <CardComponent item={item} /> : null;
};

export default PortfolioCard;
